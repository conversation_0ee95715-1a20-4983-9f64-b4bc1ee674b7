<?php
/**
 * User Model
 */

class User extends Model
{
    protected $table = 'users';
    protected $fillable = [
        'username', 'email', 'password', 'first_name', 'last_name', 'role', 'is_active'
    ];
    protected $hidden = ['password'];
    
    /**
     * Check if user is admin
     */
    public function isAdmin($user = null)
    {
        if ($user === null) {
            $user = $this->find(Auth::id());
        }
        
        return $user && $user['role'] === 'admin';
    }
    
    /**
     * Get user's full name
     */
    public function getFullName($user)
    {
        return trim($user['first_name'] . ' ' . $user['last_name']);
    }
    
    /**
     * Get active users
     */
    public function getActiveUsers()
    {
        return $this->where(['is_active' => 1], 'first_name ASC');
    }
    
    /**
     * Create new user
     */
    public function createUser($data)
    {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return $this->create($data);
    }
    
    /**
     * Update user
     */
    public function updateUser($id, $data)
    {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * Get user statistics
     */
    public function getStats()
    {
        return [
            'total_users' => $this->count(),
            'active_users' => $this->count(['is_active' => 1]),
            'admin_users' => $this->count(['role' => 'admin', 'is_active' => 1]),
            'staff_users' => $this->count(['role' => 'staff', 'is_active' => 1])
        ];
    }
}
