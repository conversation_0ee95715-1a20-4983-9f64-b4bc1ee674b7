<?php
/**
 * Profile Controller
 * Handles user profile management
 */

class ProfileController extends Controller
{
    private $userModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }
    
    /**
     * Show user profile
     */
    public function index()
    {
        $user = Auth::user();

        echo $this->view('profile/index', [
            'title' => 'My Profile',
            'user' => $user
        ]);
    }
    
    /**
     * Update user profile
     */
    public function update()
    {
        try {
            $this->checkCsrfToken();

            $user = Auth::user();

            $rules = [
                'first_name' => 'required|max:50',
                'last_name' => 'required|max:50',
                'email' => 'required|email|max:100'
            ];

            if (!empty($_POST['password'])) {
                $rules['password'] = 'min:8|confirmed';
            }

            // Check for unique email (excluding current user)
            $existingUser = $this->userModel->findBy('email', $_POST['email']);
            if ($existingUser && $existingUser['id'] != $user['id']) {
                $this->setFlashMessage('Email already exists.', 'error');
                $this->redirect('/profile');
                return;
            }

            if (!$this->validate($_POST, $rules)) {
                $this->redirect('/profile');
                return;
            }

            $data = [
                'first_name' => $_POST['first_name'],
                'last_name' => $_POST['last_name'],
                'email' => $_POST['email']
            ];

            if (!empty($_POST['password'])) {
                $data['password'] = $_POST['password'];
            }

            if ($this->userModel->updateUser($user['id'], $data)) {
                $this->setFlashMessage('Profile updated successfully!', 'success');
            } else {
                $this->setFlashMessage('Failed to update profile.', 'error');
            }

        } catch (Exception $e) {
            $this->setFlashMessage('An error occurred: ' . $e->getMessage(), 'error');
        }

        $this->redirect('/profile');
    }
}
