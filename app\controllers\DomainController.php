<?php
/**
 * Domain Controller
 */

class DomainController extends Controller
{
    private $domainModel;
    private $clientModel;
    private $mediatorModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->domainModel = new Domain();
        $this->clientModel = new Client();
        $this->mediatorModel = new Mediator();
    }
    
    /**
     * Display domains list
     */
    public function index()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $filter = $_GET['filter'] ?? '';
        
        if ($search) {
            $domains = $this->domainModel->searchDomains($search);
            $pagination = null;
        } else {
            $conditions = [];
            if ($filter === 'active') {
                $conditions['status'] = 'active';
            } elseif ($filter === 'expired') {
                $conditions['status'] = 'expired';
            } elseif ($filter === 'expiring') {
                // Get domains expiring in next 30 days
                $domains = $this->domainModel->getExpiringDomains(30);
                $pagination = null;
            } else {
                $domains = $this->domainModel->getDomainsWithClients($conditions, 'expiry_date ASC');
                $pagination = null;
            }
            
            if ($filter !== 'expiring' && empty($domains)) {
                $result = $this->domainModel->paginate($page, 20, $conditions, 'expiry_date ASC');
                $domains = $result['data'];
                $pagination = $result['pagination'];
                
                // Add client names
                foreach ($domains as &$domain) {
                    $client = $this->clientModel->find($domain['client_id']);
                    $domain['client_name'] = $client['name'] ?? 'Unknown';
                }
            }
        }
        
        echo $this->view('domains/index', [
            'title' => 'Domains',
            'domains' => $domains,
            'pagination' => $pagination,
            'search' => $search,
            'filter' => $filter,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show create domain form
     */
    public function create()
    {
        $clients = $this->clientModel->getActiveClients();
        $mediators = $this->mediatorModel->getActiveMediators();
        
        echo $this->view('domains/create', [
            'title' => 'Add New Domain',
            'clients' => $clients,
            'mediators' => $mediators,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Store new domain
     */
    public function store()
    {
        $this->checkCsrfToken();
        
        $rules = [
            'client_id' => 'required|numeric',
            'domain_name' => 'required|max:255',
            'purchase_date' => 'required|date',
            'years' => 'required|numeric|min:1|max:5',
            'current_cost' => 'numeric'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/domains/create');
        }
        
        // Calculate expiry date based on purchase date + years
        $purchaseDate = new DateTime($_POST['purchase_date']);
        $years = (int)$_POST['years'];
        $expiryDate = clone $purchaseDate;
        $expiryDate->add(new DateInterval("P{$years}Y"));

        $data = [
            'client_id' => $_POST['client_id'],
            'mediator_id' => $_POST['mediator_id'] ?? null,
            'domain_name' => strtolower($_POST['domain_name']),
            'registrar' => $_POST['registrar'] ?? null,
            'purchase_date' => $_POST['purchase_date'],
            'expiry_date' => $expiryDate->format('Y-m-d'),
            'current_cost' => $_POST['current_cost'] ?? 0,
            'auto_renewal' => isset($_POST['auto_renewal']) ? 1 : 0,
            'nameservers' => $_POST['nameservers'] ?? null,
            'status' => 'active',
            'notes' => $_POST['notes'] ?? null
        ];
        
        $domainId = $this->domainModel->create($data);
        
        if ($domainId) {
            $this->setFlashMessage('Domain added successfully!', 'success');
            $this->redirect('/domains/' . $domainId);
        } else {
            $this->setFlashMessage('Failed to add domain.', 'error');
            $this->redirect('/domains/create');
        }
    }
    
    /**
     * Show domain details
     */
    public function show($id)
    {
        $domain = $this->domainModel->getDomainWithRelations($id);
        
        if (!$domain) {
            $this->setFlashMessage('Domain not found.', 'error');
            $this->redirect('/domains');
        }
        
        echo $this->view('domains/show', [
            'title' => 'Domain Details - ' . $domain['domain_name'],
            'domain' => $domain,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show edit domain form
     */
    public function edit($id)
    {
        $domain = $this->domainModel->find($id);
        
        if (!$domain) {
            $this->setFlashMessage('Domain not found.', 'error');
            $this->redirect('/domains');
        }
        
        $clients = $this->clientModel->getActiveClients();
        $mediators = $this->mediatorModel->getActiveMediators();
        
        echo $this->view('domains/edit', [
            'title' => 'Edit Domain - ' . $domain['domain_name'],
            'domain' => $domain,
            'clients' => $clients,
            'mediators' => $mediators,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update domain
     */
    public function update($id)
    {
        $this->checkCsrfToken();
        
        $domain = $this->domainModel->find($id);
        if (!$domain) {
            $this->setFlashMessage('Domain not found.', 'error');
            $this->redirect('/domains');
        }
        
        $rules = [
            'client_id' => 'required|numeric',
            'domain_name' => 'required|max:255',
            'purchase_date' => 'required|date',
            'expiry_date' => 'required|date',
            'current_cost' => 'numeric'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/domains/' . $id . '/edit');
        }
        
        $data = [
            'client_id' => $_POST['client_id'],
            'mediator_id' => $_POST['mediator_id'] ?? null,
            'domain_name' => strtolower($_POST['domain_name']),
            'registrar' => $_POST['registrar'] ?? null,
            'purchase_date' => $_POST['purchase_date'],
            'expiry_date' => $_POST['expiry_date'],
            'current_cost' => $_POST['current_cost'] ?? 0,
            'auto_renewal' => isset($_POST['auto_renewal']) ? 1 : 0,
            'nameservers' => $_POST['nameservers'] ?? null,
            'status' => $_POST['status'] ?? 'active',
            'notes' => $_POST['notes'] ?? null
        ];
        
        if ($this->domainModel->update($id, $data)) {
            $this->setFlashMessage('Domain updated successfully!', 'success');
            $this->redirect('/domains/' . $id);
        } else {
            $this->setFlashMessage('Failed to update domain.', 'error');
            $this->redirect('/domains/' . $id . '/edit');
        }
    }
    
    /**
     * Delete domain
     */
    public function delete($id)
    {
        $this->checkCsrfToken();
        
        $domain = $this->domainModel->find($id);
        if (!$domain) {
            $this->setFlashMessage('Domain not found.', 'error');
            $this->redirect('/domains');
        }
        
        if ($this->domainModel->delete($id)) {
            $this->setFlashMessage('Domain deleted successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to delete domain.', 'error');
        }
        
        $this->redirect('/domains');
    }
    
    /**
     * Bulk update domain statuses
     */
    public function bulkUpdate()
    {
        $this->checkCsrfToken();
        
        $action = $_POST['action'] ?? '';
        $domainIds = $_POST['domain_ids'] ?? [];
        
        if (empty($domainIds) || !is_array($domainIds)) {
            $this->setFlashMessage('No domains selected.', 'error');
            $this->redirect('/domains');
        }
        
        $updated = 0;
        foreach ($domainIds as $id) {
            if ($action === 'activate') {
                $this->domainModel->update($id, ['status' => 'active']);
                $updated++;
            } elseif ($action === 'deactivate') {
                $this->domainModel->update($id, ['status' => 'suspended']);
                $updated++;
            } elseif ($action === 'delete') {
                $this->domainModel->delete($id);
                $updated++;
            }
        }
        
        $this->setFlashMessage("$updated domains updated successfully!", 'success');
        $this->redirect('/domains');
    }
    
    /**
     * Export domains data
     */
    public function export()
    {
        $format = $_GET['format'] ?? 'csv';
        $domains = $this->domainModel->getDomainsWithClients([], 'domain_name ASC');
        
        if ($format === 'csv') {
            $this->exportCSV($domains);
        } else {
            $this->setFlashMessage('Invalid export format.', 'error');
            $this->redirect('/domains');
        }
    }
    
    /**
     * Export domains to CSV
     */
    private function exportCSV($domains)
    {
        $filename = 'domains_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'Domain Name', 'Client', 'Registrar', 'Purchase Date', 'Expiry Date', 
            'Renewal Cost', 'Status', 'Auto Renewal', 'Days Until Expiry'
        ]);
        
        // CSV data
        foreach ($domains as $domain) {
            $daysUntilExpiry = Helper::daysBetween(date('Y-m-d'), $domain['expiry_date']);
            
            fputcsv($output, [
                $domain['domain_name'],
                $domain['client_name'],
                $domain['registrar'],
                $domain['purchase_date'],
                $domain['expiry_date'],
                $domain['current_cost'],
                $domain['status'],
                $domain['auto_renewal'] ? 'Yes' : 'No',
                $daysUntilExpiry
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * Get domains for a specific client (AJAX endpoint)
     */
    public function getClientDomains()
    {
        header('Content-Type: application/json');

        $clientId = $_GET['client_id'] ?? null;
        if (!$clientId) {
            echo json_encode(['error' => 'Client ID required']);
            exit;
        }

        $domains = $this->domainModel->getClientDomains($clientId);
        echo json_encode($domains);
        exit;
    }

    /**
     * Get WhatsApp data for domain (AJAX endpoint)
     */
    public function getWhatsAppData($id)
    {
        header('Content-Type: application/json');

        $domain = $this->domainModel->getDomainWithRelations($id);

        if (!$domain) {
            echo json_encode(['success' => false, 'error' => 'Domain not found']);
            exit;
        }

        $whatsappUrl = Helper::generateWhatsAppRenewalUrl(
            'domain',
            $domain['domain_name'],
            $domain['expiry_date'],
            $domain['client_phone'],
            $domain['mediator_phone']
        );

        echo json_encode([
            'success' => true,
            'whatsapp_url' => $whatsappUrl,
            'has_mediator_phone' => !empty($domain['mediator_phone']),
            'has_client_phone' => !empty($domain['client_phone'])
        ]);
        exit;
    }
}
