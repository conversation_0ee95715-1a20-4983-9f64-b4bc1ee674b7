<?php
/**
 * Settings Controller
 */

class SettingsController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        
        // Check admin permission for most settings
        if (!Auth::isAdmin()) {
            $this->setFlashMessage('Access denied. Admin privileges required.', 'error');
            $this->redirect('/dashboard');
        }
    }
    
    /**
     * Settings dashboard
     */
    public function index()
    {
        $systemInfo = Helper::getSystemInfo();
        $cacheStats = $this->cache->stats();
        
        echo $this->view('settings/index', [
            'title' => 'System Settings',
            'system_info' => $systemInfo,
            'cache_stats' => $cacheStats,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Application settings
     */
    public function application()
    {
        echo $this->view('settings/application', [
            'title' => 'Application Settings',
            'config' => $this->config,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update application settings
     */
    public function updateApplication()
    {
        $this->checkCsrfToken();
        
        $rules = [
            'app_name' => 'required|max:100',
            'admin_email' => 'required|email',
            'timezone' => 'required'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/settings/application');
        }
        
        // Update configuration file
        $configPath = CONFIG_PATH . '/config.php';
        $config = include $configPath;
        
        $config['app']['name'] = $_POST['app_name'];
        $config['app']['admin_email'] = $_POST['admin_email'];
        $config['app']['timezone'] = $_POST['timezone'];
        
        if ($this->updateConfigFile($configPath, $config)) {
            $this->setFlashMessage('Application settings updated successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to update settings.', 'error');
        }
        
        $this->redirect('/settings/application');
    }
    
    /**
     * Email settings
     */
    public function email()
    {
        echo $this->view('settings/email', [
            'title' => 'Email Settings',
            'config' => $this->config,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update email settings
     */
    public function updateEmail()
    {
        $this->checkCsrfToken();
        
        $rules = [
            'mail_host' => 'required',
            'mail_port' => 'required|numeric',
            'mail_username' => 'required|email',
            'mail_password' => 'required',
            'from_address' => 'required|email',
            'from_name' => 'required'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/settings/email');
        }
        
        // Update configuration file
        $configPath = CONFIG_PATH . '/config.php';
        $config = include $configPath;
        
        $config['mail']['host'] = $_POST['mail_host'];
        $config['mail']['port'] = (int)$_POST['mail_port'];
        $config['mail']['username'] = $_POST['mail_username'];
        $config['mail']['password'] = $_POST['mail_password'];
        $config['mail']['encryption'] = $_POST['mail_encryption'];
        $config['mail']['from_address'] = $_POST['from_address'];
        $config['mail']['from_name'] = $_POST['from_name'];
        
        if ($this->updateConfigFile($configPath, $config)) {
            $this->setFlashMessage('Email settings updated successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to update email settings.', 'error');
        }
        
        $this->redirect('/settings/email');
    }
    
    /**
     * Test email configuration
     */
    public function testEmail()
    {
        $this->checkCsrfToken();
        
        $testEmail = $_POST['test_email'] ?? Auth::user()['email'];
        
        if (!$testEmail) {
            $this->setFlashMessage('Please provide a test email address.', 'error');
            $this->redirect('/settings/email');
        }
        
        $subject = 'Test Email from ' . $this->config['app']['name'];
        $message = '<h2>Test Email</h2><p>This is a test email to verify your email configuration.</p><p>Sent at: ' . date('Y-m-d H:i:s') . '</p>';
        
        if (Helper::sendEmail($testEmail, $subject, $message)) {
            $this->setFlashMessage('Test email sent successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to send test email. Please check your configuration.', 'error');
        }
        
        $this->redirect('/settings/email');
    }
    
    /**
     * Notification settings
     */
    public function notifications()
    {
        echo $this->view('settings/notifications', [
            'title' => 'Notification Settings',
            'config' => $this->config,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update notification settings
     */
    public function updateNotifications()
    {
        $this->checkCsrfToken();
        
        // Update configuration file
        $configPath = CONFIG_PATH . '/config.php';
        $config = include $configPath;
        
        $config['notifications']['domain_expiry_alert_days'] = array_map('intval', $_POST['domain_expiry_alert_days'] ?? [30, 15, 7, 1]);
        $config['notifications']['server_expiry_alert_days'] = array_map('intval', $_POST['server_expiry_alert_days'] ?? [30, 15, 7, 1]);
        $config['notifications']['email_notifications'] = isset($_POST['email_notifications']);
        $config['notifications']['dashboard_notifications'] = isset($_POST['dashboard_notifications']);
        
        if ($this->updateConfigFile($configPath, $config)) {
            $this->setFlashMessage('Notification settings updated successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to update notification settings.', 'error');
        }
        
        $this->redirect('/settings/notifications');
    }
    
    /**
     * Cache management
     */
    public function cache()
    {
        $stats = $this->cache->stats();
        
        echo $this->view('settings/cache', [
            'title' => 'Cache Management',
            'stats' => $stats,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Clear cache
     */
    public function clearCache()
    {
        $this->checkCsrfToken();
        
        if ($this->cache->flush()) {
            $this->setFlashMessage('Cache cleared successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to clear cache.', 'error');
        }
        
        $this->redirect('/settings/cache');
    }
    
    /**
     * Clean expired cache
     */
    public function cleanCache()
    {
        $this->checkCsrfToken();
        
        $cleaned = $this->cache->clean();
        $this->setFlashMessage("Cleaned {$cleaned} expired cache entries.", 'success');
        
        $this->redirect('/settings/cache');
    }
    
    /**
     * Backup management
     */
    public function backup()
    {
        $backupDir = ROOT_PATH . '/backups';
        $backups = [];
        
        if (is_dir($backupDir)) {
            $files = glob($backupDir . '/backup_*.sql');
            foreach ($files as $file) {
                $backups[] = [
                    'filename' => basename($file),
                    'size' => filesize($file),
                    'created' => filemtime($file)
                ];
            }
            
            // Sort by creation time (newest first)
            usort($backups, function($a, $b) {
                return $b['created'] - $a['created'];
            });
        }
        
        echo $this->view('settings/backup', [
            'title' => 'Backup Management',
            'backups' => $backups,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Create backup
     */
    public function createBackup()
    {
        $this->checkCsrfToken();
        
        $filename = Helper::backupDatabase();
        
        if ($filename) {
            $this->setFlashMessage("Backup created successfully: {$filename}", 'success');
        } else {
            $this->setFlashMessage('Failed to create backup.', 'error');
        }
        
        $this->redirect('/settings/backup');
    }
    
    /**
     * Download backup
     */
    public function downloadBackup($filename)
    {
        $backupPath = ROOT_PATH . '/backups/' . $filename;
        
        if (!file_exists($backupPath) || !preg_match('/^backup_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.sql$/', $filename)) {
            $this->setFlashMessage('Backup file not found.', 'error');
            $this->redirect('/settings/backup');
        }
        
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($backupPath));
        
        readfile($backupPath);
        exit;
    }
    
    /**
     * Delete backup
     */
    public function deleteBackup($filename)
    {
        $this->checkCsrfToken();
        
        $backupPath = ROOT_PATH . '/backups/' . $filename;
        
        if (!file_exists($backupPath) || !preg_match('/^backup_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.sql$/', $filename)) {
            $this->setFlashMessage('Backup file not found.', 'error');
            $this->redirect('/settings/backup');
        }
        
        if (unlink($backupPath)) {
            $this->setFlashMessage('Backup deleted successfully.', 'success');
        } else {
            $this->setFlashMessage('Failed to delete backup.', 'error');
        }
        
        $this->redirect('/settings/backup');
    }
    
    /**
     * Update configuration file
     */
    private function updateConfigFile($path, $config)
    {
        $content = "<?php\n/**\n * Application Configuration\n */\n\nreturn " . var_export($config, true) . ";\n";
        
        return file_put_contents($path, $content) !== false;
    }
}
