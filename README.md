# Client Domain & Server Management System

A comprehensive PHP web application for managing client domain and server data with expiry tracking, notifications, and reporting capabilities.

## Features

### Core Functionality
- **Client Management** - Store client information, contact details, and notes
- **Domain Management** - Track domain purchases, expiry dates, and renewal costs
- **Server Management** - Monitor server details, specifications, and renewal schedules
- **Mediator Management** - Manage mediator information and commission tracking
- **Expiry Alerts** - Automated notifications for upcoming domain/server expirations
- **Dashboard** - Comprehensive overview with statistics and alerts

### Technical Features
- **MVC Architecture** - Clean, organized code structure
- **Mobile Responsive** - Bootstrap 5 CSS framework
- **Caching System** - File-based caching for improved performance
- **Environment Detection** - Automatic XAMPP/Live server compatibility
- **Auto Database Setup** - Automatic database creation and migrations
- **Role-Based Access** - Admin and Staff user roles
- **Audit Trail** - Track all user activities and changes
- **Export Capabilities** - PDF and Excel report generation

### Security Features
- **CSRF Protection** - Cross-site request forgery prevention
- **SQL Injection Prevention** - Prepared statements and input validation
- **XSS Protection** - Output escaping and content security
- **Session Management** - Secure session handling with timeouts
- **Password Hashing** - Bcrypt password encryption
- **Failed Login Protection** - Account lockout after failed attempts

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher (or MariaDB 10.2+)
- Apache web server with mod_rewrite
- 50MB disk space minimum

## Installation

### XAMPP Local Setup

1. **Download and Install XAMPP**
   - Download from [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Install and start Apache and MySQL services

2. **Clone/Download the Project**
   ```bash
   cd C:\xampp\htdocs\projects
   # Clone or extract the project files to 'nicetech' folder
   ```

3. **Configure Database**
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - The system will automatically create the database on first run
   - Default database name: `client_manager`

4. **Access the Application**
   - Open browser and go to: `http://localhost/projects/nicetech`
   - The system will automatically set up the database and create default admin user

5. **Default Login Credentials**
   - Username: `admin`
   - Password: `admin123`
   - **Important**: Change the default password after first login

### Live Server Setup

1. **Upload Files**
   - Upload all files to your web server's document root
   - Ensure proper file permissions (755 for directories, 644 for files)

2. **Configure Database**
   - Update database credentials in `config/database.php`
   - Set your live server database details

3. **Update Configuration**
   - Edit `config/config.php` with your live server settings
   - Update the app URL and email settings

4. **Set Permissions**
   ```bash
   chmod 755 cache/ logs/ backups/
   chmod 644 config/*.php
   ```

## Configuration

### Database Configuration
Edit `config/database.php`:
```php
'connections' => [
    'mysql' => [
        'host' => 'localhost',
        'database' => 'your_database_name',
        'username' => 'your_username',
        'password' => 'your_password',
    ]
]
```

### Application Configuration
Edit `config/config.php`:
```php
'app' => [
    'name' => 'Your Company Name',
    'url' => 'https://yourdomain.com',
    'admin_email' => '<EMAIL>'
],
'mail' => [
    'host' => 'smtp.gmail.com',
    'username' => '<EMAIL>',
    'password' => 'your-app-password'
]
```

## Usage

### Managing Clients
1. Navigate to **Clients** section
2. Click **Add New Client** to create client records
3. Fill in client details including contact information
4. Save and manage client data

### Managing Domains
1. Go to **Domains** section
2. Click **Add New Domain** 
3. Select client, enter domain details, purchase and expiry dates
4. Set renewal costs and auto-renewal preferences
5. Track expiry dates and receive alerts

### Managing Servers
1. Access **Servers** section
2. Click **Add New Server**
3. Enter server specifications, provider details
4. Set purchase and expiry dates
5. Monitor server status and renewals

### Dashboard Overview
- View statistics for clients, domains, and servers
- Monitor upcoming expirations (30-day alerts)
- Track revenue and renewal costs
- Review recent system activities

### Reports and Exports
1. Navigate to **Reports** section
2. Generate various reports:
   - Client summary reports
   - Domain expiry reports
   - Server renewal schedules
   - Revenue analysis
3. Export data in PDF or Excel format

## User Management

### Admin Functions
- Create and manage staff users
- Access all system settings
- View audit logs and system statistics
- Manage backups and system maintenance

### Staff Functions
- Manage clients, domains, and servers
- View reports and dashboards
- Limited access to system settings

## Maintenance

### Automatic Features
- **Database Backups** - Scheduled automatic backups
- **Cache Cleaning** - Automatic cache management
- **Expiry Notifications** - Automated email alerts
- **Status Updates** - Automatic status updates for expired items

### Manual Maintenance
- Regular password updates
- Database optimization
- Log file cleanup
- Security updates

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists and user has permissions

2. **Permission Errors**
   - Set proper file permissions (755/644)
   - Ensure web server can write to cache and logs directories

3. **URL Rewriting Issues**
   - Ensure mod_rewrite is enabled in Apache
   - Check .htaccess file is present and readable

4. **Email Notifications Not Working**
   - Verify SMTP settings in `config/config.php`
   - Check email credentials and server settings

### Debug Mode
Enable debug mode for development:
- Set `DEBUG_MODE` to `true` in your environment
- View detailed error messages and logs

## Support

For technical support or questions:
- Check the documentation
- Review error logs in `/logs` directory
- Contact system administrator

## Security Notes

- Change default admin password immediately
- Use strong passwords for all accounts
- Keep the system updated
- Regular security audits recommended
- Backup data regularly

## License

This software is proprietary. All rights reserved.

---

**Version**: 1.0.0  
**Last Updated**: <?= date('Y-m-d') ?>  
**Developed by**: Your Development Team
