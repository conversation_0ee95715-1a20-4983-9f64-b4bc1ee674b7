<?php
/**
 * Database Configuration
 */

return [
    'default' => 'mysql',
    
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => IS_LOCAL ? 'localhost' : 'localhost',
            'port' => '3306',
            'database' => 'client_manager',
            'username' => IS_LOCAL ? 'root' : 'your_db_user',
            'password' => IS_LOCAL ? '' : 'your_db_password',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        ],
        
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => DATABASE_PATH . '/database.sqlite',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        ]
    ],
    
    'migrations_table' => 'migrations',
    'migration_path' => DATABASE_PATH . '/migrations'
];
