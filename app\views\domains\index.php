<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-globe me-2 text-primary"></i>
                Domain Management
            </h1>
            <div>
                <a href="<?= url('/domains/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add New Domain
                </a>
                <div class="btn-group ms-2">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?= url('/domains/export?format=csv') ?>">
                            <i class="fas fa-file-csv me-1"></i> Export CSV
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="Search domains..." value="<?= e($search) ?>">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i>
            </button>
            <?php if ($search): ?>
                <a href="<?= url('/domains') ?>" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-times"></i>
                </a>
            <?php endif; ?>
        </form>
    </div>
    <div class="col-md-6">
        <div class="btn-group w-100" role="group">
            <a href="<?= url('/domains') ?>" 
               class="btn <?= $filter === '' ? 'btn-primary' : 'btn-outline-primary' ?>">
                All
            </a>
            <a href="<?= url('/domains?filter=active') ?>" 
               class="btn <?= $filter === 'active' ? 'btn-primary' : 'btn-outline-primary' ?>">
                Active
            </a>
            <a href="<?= url('/domains?filter=expiring') ?>" 
               class="btn <?= $filter === 'expiring' ? 'btn-warning' : 'btn-outline-warning' ?>">
                Expiring
            </a>
            <a href="<?= url('/domains?filter=expired') ?>" 
               class="btn <?= $filter === 'expired' ? 'btn-danger' : 'btn-outline-danger' ?>">
                Expired
            </a>
        </div>
    </div>
</div>

<!-- Domains Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-1"></i>
                    Domains List
                    <?php if ($search): ?>
                        <small class="text-muted">- Search results for "<?= e($search) ?>"</small>
                    <?php endif; ?>
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($domains)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No domains found</h5>
                        <p class="text-muted">
                            <?php if ($search): ?>
                                No domains match your search criteria.
                            <?php else: ?>
                                Get started by adding your first domain.
                            <?php endif; ?>
                        </p>
                        <?php if (!$search): ?>
                            <a href="<?= url('/domains/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Add First Domain
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Domain Name</th>
                                    <th>Client</th>
                                    <th>Registrar</th>
                                    <th>Expiry Date</th>
                                    <th>Status</th>
                                    <th>Current Cost</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($domains as $domain): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-globe text-primary me-2"></i>
                                            <div>
                                                <strong><?= e($domain['domain_name']) ?></strong>
                                                <?php if ($domain['auto_renewal']): ?>
                                                    <span class="badge bg-info ms-1" title="Auto Renewal Enabled">
                                                        <i class="fas fa-sync-alt"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="<?= url('/clients/' . $domain['client_id']) ?>" class="text-decoration-none">
                                            <?= e($domain['client_name']) ?>
                                        </a>
                                    </td>
                                    <td><?= e($domain['registrar']) ?: '<span class="text-muted">-</span>' ?></td>
                                    <td>
                                        <?php 
                                        $daysUntil = days_until($domain['expiry_date']);
                                        $expiryClass = '';
                                        if ($daysUntil < 0) {
                                            $expiryClass = 'text-danger';
                                        } elseif ($daysUntil <= 7) {
                                            $expiryClass = 'text-danger';
                                        } elseif ($daysUntil <= 30) {
                                            $expiryClass = 'text-warning';
                                        } else {
                                            $expiryClass = 'text-success';
                                        }
                                        ?>
                                        <span class="<?= $expiryClass ?>">
                                            <?= format_date($domain['expiry_date'], 'M j, Y') ?>
                                        </span>
                                        <br>
                                        <small class="<?= $expiryClass ?>">
                                            <?php if ($daysUntil < 0): ?>
                                                <?= abs($daysUntil) ?> days ago
                                            <?php else: ?>
                                                <?= $daysUntil ?> days left
                                            <?php endif; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?= status_badge($domain['status']) ?>
                                    </td>
                                    <td>
                                        <?php if ($domain['current_cost'] > 0): ?>
                                            <?= format_currency($domain['current_cost']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= url('/domains/' . $domain['id']) ?>"
                                               class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= url('/domains/' . $domain['id'] . '/edit') ?>"
                                               class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="sendWhatsAppReminder(<?= $domain['id'] ?>, 'domain', '<?= e($domain['domain_name']) ?>', '<?= $domain['expiry_date'] ?>')"
                                                    title="Send WhatsApp Renewal Reminder">
                                                <i class="fab fa-whatsapp"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteDomain(<?= $domain['id'] ?>, '<?= e($domain['domain_name']) ?>')"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination && $pagination['total_pages'] > 1): ?>
                        <nav aria-label="Domains pagination" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($pagination['has_previous']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= url('/domains?page=' . ($pagination['current_page'] - 1) . ($search ? '&search=' . urlencode($search) : '') . ($filter ? '&filter=' . $filter : '')) ?>">
                                            Previous
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                                    <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                        <a class="page-link" href="<?= url('/domains?page=' . $i . ($search ? '&search=' . urlencode($search) : '') . ($filter ? '&filter=' . $filter : '')) ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= url('/domains?page=' . ($pagination['current_page'] + 1) . ($search ? '&search=' . urlencode($search) : '') . ($filter ? '&filter=' . $filter : '')) ?>">
                                            Next
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        
                        <div class="text-center text-muted">
                            <small>
                                Showing <?= $pagination['current_page'] * $pagination['per_page'] - $pagination['per_page'] + 1 ?> 
                                to <?= min($pagination['current_page'] * $pagination['per_page'], $pagination['total_items']) ?> 
                                of <?= $pagination['total_items'] ?> domains
                            </small>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete domain <strong id="domainName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <?= method_field('DELETE') ?>
                    <button type="submit" class="btn btn-danger">Delete Domain</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteDomain(id, name) {
    document.getElementById('domainName').textContent = name;
    document.getElementById('deleteForm').action = '<?= url('/domains/') ?>' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function sendWhatsAppReminder(id, type, name, expiryDate) {
    // Fetch domain details with phone numbers
    fetch('<?= url('/domains/') ?>' + id + '/whatsapp-data')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.whatsapp_url) {
                window.open(data.whatsapp_url, '_blank');
            } else {
                alert('No WhatsApp number available for this ' + type + '. Please add a phone number to the client or mediator.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to generate WhatsApp link. Please try again.');
        });
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
