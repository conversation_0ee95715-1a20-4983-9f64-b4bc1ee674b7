<?php
/**
 * View Class
 * Handles template rendering and view management
 */

class View
{
    private $data = [];
    private $viewPath;
    
    public function __construct()
    {
        $this->viewPath = APP_PATH . '/views';
    }
    
    /**
     * Set view data
     */
    public function set($key, $value = null)
    {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }
    }
    
    /**
     * Get view data
     */
    public function get($key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }
    
    /**
     * Render view template
     */
    public function render($template, $data = [])
    {
        // Merge data
        $viewData = array_merge($this->data, $data);
        
        // Extract variables for use in template
        extract($viewData);
        
        // Add helper functions
        $this->addHelperFunctions();
        
        // Start output buffering
        ob_start();
        
        // Include template file
        $templateFile = $this->viewPath . '/' . $template . '.php';
        
        if (!file_exists($templateFile)) {
            throw new Exception("View template not found: {$template}");
        }
        
        include $templateFile;
        
        // Get content and clean buffer
        $content = ob_get_clean();
        
        return $content;
    }
    
    /**
     * Render partial template
     */
    public function partial($template, $data = [])
    {
        return $this->render($template, $data);
    }
    
    /**
     * Include template with layout
     */
    public function layout($layout, $template, $data = [])
    {
        // Render main content
        $content = $this->render($template, $data);
        
        // Render with layout
        return $this->render($layout, array_merge($data, ['content' => $content]));
    }
    
    /**
     * Add helper functions for templates
     */
    private function addHelperFunctions()
    {
        // URL helper
        if (!function_exists('url')) {
            function url($path = '') {
                $config = include CONFIG_PATH . '/config.php';
                $baseUrl = $config['app']['url'];
                return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
            }
        }
        
        // Asset helper
        if (!function_exists('asset')) {
            function asset($path) {
                return url('public/' . ltrim($path, '/'));
            }
        }
        
        // CSRF token helper
        if (!function_exists('csrf_token')) {
            function csrf_token() {
                if (!isset($_SESSION['csrf_token'])) {
                    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                }
                return $_SESSION['csrf_token'];
            }
        }
        
        // CSRF field helper
        if (!function_exists('csrf_field')) {
            function csrf_field() {
                return '<input type="hidden" name="_token" value="' . csrf_token() . '">';
            }
        }
        
        // Method field helper
        if (!function_exists('method_field')) {
            function method_field($method) {
                return '<input type="hidden" name="_method" value="' . strtoupper($method) . '">';
            }
        }
        
        // Old input helper
        if (!function_exists('old')) {
            function old($key, $default = '') {
                return $_SESSION['old_input'][$key] ?? $default;
            }
        }
        
        // Flash message helper
        if (!function_exists('flash_messages')) {
            function flash_messages() {
                $messages = $_SESSION['flash_messages'] ?? [];
                unset($_SESSION['flash_messages']);
                return $messages;
            }
        }
        
        // Validation error helper
        if (!function_exists('error')) {
            function error($field) {
                return $_SESSION['validation_errors'][$field] ?? null;
            }
        }
        
        // Has error helper
        if (!function_exists('has_error')) {
            function has_error($field) {
                return isset($_SESSION['validation_errors'][$field]);
            }
        }
        
        // Escape HTML helper
        if (!function_exists('e')) {
            function e($string) {
                return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
            }
        }
        
        // Format date helper
        if (!function_exists('format_date')) {
            function format_date($date, $format = 'Y-m-d') {
                if (!$date) return '';
                return date($format, strtotime($date));
            }
        }
        
        // Format currency helper
        if (!function_exists('format_currency')) {
            function format_currency($amount, $currency = '₹') {
                return $currency . number_format($amount, 2);
            }
        }
        
        // Truncate text helper
        if (!function_exists('truncate')) {
            function truncate($text, $length = 100, $suffix = '...') {
                if (strlen($text) <= $length) {
                    return $text;
                }
                return substr($text, 0, $length) . $suffix;
            }
        }
        
        // Active menu helper
        if (!function_exists('active_menu')) {
            function active_menu($path, $class = 'active') {
                $currentPath = $_SERVER['REQUEST_URI'];
                $basePath = dirname($_SERVER['SCRIPT_NAME']);
                
                if ($basePath !== '/') {
                    $currentPath = str_replace($basePath, '', $currentPath);
                }
                
                return (strpos($currentPath, $path) === 0) ? $class : '';
            }
        }
        
        // Check permission helper
        if (!function_exists('can')) {
            function can($permission) {
                $user = Auth::user();
                if (!$user) return false;
                
                // Simple role-based check
                if ($permission === 'admin') {
                    return $user['role'] === 'admin';
                }
                
                return true; // Default allow for staff
            }
        }
        
        // Avatar helper
        if (!function_exists('avatar')) {
            function avatar($user, $size = 40) {
                // Handle different possible field names
                $firstName = $user['first_name'] ?? $user['name'] ?? 'U';
                $lastName = $user['last_name'] ?? '';

                // If no last name and we have a full name, try to split it
                if (empty($lastName) && !empty($firstName) && strpos($firstName, ' ') !== false) {
                    $nameParts = explode(' ', $firstName, 2);
                    $firstName = $nameParts[0];
                    $lastName = $nameParts[1] ?? '';
                }

                // Create initials
                $firstInitial = !empty($firstName) ? strtoupper(substr($firstName, 0, 1)) : 'U';
                $lastInitial = !empty($lastName) ? strtoupper(substr($lastName, 0, 1)) : '';
                $initials = $firstInitial . $lastInitial;

                // If we only have one initial, use the first two characters of the name
                if (strlen($initials) === 1 && strlen($firstName) > 1) {
                    $initials = strtoupper(substr($firstName, 0, 2));
                }

                return '<div class="avatar" style="width: ' . $size . 'px; height: ' . $size . 'px; background: #007bff; color: white; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; font-weight: bold; font-size: ' . ($size * 0.4) . 'px;">' . $initials . '</div>';
            }
        }
        
        // Status badge helper
        if (!function_exists('status_badge')) {
            function status_badge($status) {
                $classes = [
                    'active' => 'bg-success',
                    'expired' => 'bg-danger',
                    'suspended' => 'bg-warning',
                    'terminated' => 'bg-dark',
                    'transferred' => 'bg-info',
                    'pending' => 'bg-warning'
                ];

                $class = $classes[$status] ?? 'bg-secondary';
                return '<span class="badge ' . $class . '">' . ucfirst($status) . '</span>';
            }
        }
        
        // Days until helper
        if (!function_exists('days_until')) {
            function days_until($date) {
                if (!$date) return null;
                
                $now = new DateTime();
                $target = new DateTime($date);
                $diff = $now->diff($target);
                
                if ($target < $now) {
                    return -$diff->days; // Negative for past dates
                }
                
                return $diff->days;
            }
        }
        
        // Expiry alert helper
        if (!function_exists('expiry_alert')) {
            function expiry_alert($date, $threshold = 30) {
                $days = days_until($date);

                if ($days === null) return '';

                if ($days < 0) {
                    return '<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> Expired</span>';
                } elseif ($days <= 7) {
                    return '<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> ' . $days . ' days</span>';
                } elseif ($days <= $threshold) {
                    return '<span class="text-warning"><i class="fas fa-clock"></i> ' . $days . ' days</span>';
                }

                return '<span class="text-success">' . $days . ' days</span>';
            }
        }

        // Auth user helper
        if (!function_exists('auth_user')) {
            function auth_user() {
                return Auth::user();
            }
        }

        // Time ago helper
        if (!function_exists('time_ago')) {
            function time_ago($datetime) {
                if (!$datetime) return 'Never';

                $time = time() - strtotime($datetime);

                if ($time < 60) return 'just now';
                if ($time < 3600) return floor($time/60) . ' minutes ago';
                if ($time < 86400) return floor($time/3600) . ' hours ago';
                if ($time < 2592000) return floor($time/86400) . ' days ago';
                if ($time < 31536000) return floor($time/2592000) . ' months ago';

                return floor($time/31536000) . ' years ago';
            }
        }
    }
}
