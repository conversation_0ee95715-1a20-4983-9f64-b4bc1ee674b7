<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-server me-2 text-primary"></i>
                Edit Server - <?= e($server['server_name']) ?>
            </h1>
            <a href="<?= url('/servers/' . $server['id']) ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Server
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Server Information
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/servers/' . $server['id']) ?>" id="serverForm">
                    <?= csrf_field() ?>
                    <?= method_field('PUT') ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_id" class="form-label">
                                    Client <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('client_id') ? 'is-invalid' : '' ?>" 
                                        id="client_id" 
                                        name="client_id" 
                                        required>
                                    <option value="">Select a client</option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?= $client['id'] ?>" <?= old('client_id', $server['client_id']) == $client['id'] ? 'selected' : '' ?>>
                                            <?= e($client['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('client_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('client_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mediator_id" class="form-label">Mediator</label>
                                <select class="form-select <?= has_error('mediator_id') ? 'is-invalid' : '' ?>" 
                                        id="mediator_id" 
                                        name="mediator_id">
                                    <option value="">No mediator</option>
                                    <?php foreach ($mediators as $mediator): ?>
                                        <option value="<?= $mediator['id'] ?>" <?= old('mediator_id', $server['mediator_id']) == $mediator['id'] ? 'selected' : '' ?>>
                                            <?= e($mediator['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('mediator_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('mediator_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="server_name" class="form-label">
                                    Server Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?= has_error('server_name') ? 'is-invalid' : '' ?>" 
                                       id="server_name" 
                                       name="server_name" 
                                       value="<?= e(old('server_name', $server['server_name'])) ?>"
                                       placeholder="Web Server 1, Mail Server, etc."
                                       required>
                                <?php if (has_error('server_name')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('server_name')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="provider" class="form-label">Provider</label>
                                <input type="text" 
                                       class="form-control <?= has_error('provider') ? 'is-invalid' : '' ?>" 
                                       id="provider" 
                                       name="provider" 
                                       value="<?= e(old('provider', $server['provider'])) ?>"
                                       placeholder="AWS, DigitalOcean, etc.">
                                <?php if (has_error('provider')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('provider')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="server_type" class="form-label">
                                    Server Type <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('server_type') ? 'is-invalid' : '' ?>" 
                                        id="server_type" 
                                        name="server_type" 
                                        required>
                                    <option value="">Select server type</option>
                                    <?php foreach ($server_types as $type): ?>
                                        <option value="<?= $type ?>" <?= old('server_type', $server['server_type']) == $type ? 'selected' : '' ?>>
                                            <?= ucfirst($type) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('server_type')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('server_type')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ip_address" class="form-label">IP Address</label>
                                <input type="text" 
                                       class="form-control <?= has_error('ip_address') ? 'is-invalid' : '' ?>" 
                                       id="ip_address" 
                                       name="ip_address" 
                                       value="<?= e(old('ip_address', $server['ip_address'])) ?>"
                                       placeholder="***********">
                                <?php if (has_error('ip_address')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('ip_address')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="purchase_date" class="form-label">
                                    Purchase Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control <?= has_error('purchase_date') ? 'is-invalid' : '' ?>" 
                                       id="purchase_date" 
                                       name="purchase_date" 
                                       value="<?= e(old('purchase_date', $server['purchase_date'])) ?>"
                                       required>
                                <?php if (has_error('purchase_date')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('purchase_date')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">
                                    Expiry Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control <?= has_error('expiry_date') ? 'is-invalid' : '' ?>" 
                                       id="expiry_date" 
                                       name="expiry_date" 
                                       value="<?= e(old('expiry_date', $server['expiry_date'])) ?>"
                                       required>
                                <?php if (has_error('expiry_date')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('expiry_date')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="current_cost" class="form-label">Current Cost</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number"
                                           class="form-control <?= has_error('current_cost') ? 'is-invalid' : '' ?>"
                                           id="current_cost"
                                           name="current_cost"
                                           value="<?= e(old('current_cost', $server['current_cost'])) ?>"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                    <?php if (has_error('current_cost')): ?>
                                        <div class="invalid-feedback">
                                            <?= e(error('current_cost')) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="form-text">Amount you are charging the client now</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select <?= has_error('status') ? 'is-invalid' : '' ?>" 
                                        id="status" 
                                        name="status">
                                    <option value="active" <?= old('status', $server['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="expired" <?= old('status', $server['status']) == 'expired' ? 'selected' : '' ?>>Expired</option>
                                    <option value="suspended" <?= old('status', $server['status']) == 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                    <option value="pending" <?= old('status', $server['status']) == 'pending' ? 'selected' : '' ?>>Pending</option>
                                </select>
                                <?php if (has_error('status')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('status')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="auto_renewal" 
                                   name="auto_renewal" 
                                   value="1"
                                   <?= old('auto_renewal', $server['auto_renewal']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="auto_renewal">
                                Enable Auto Renewal
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="specifications" class="form-label">Server Specifications</label>
                        <textarea class="form-control <?= has_error('specifications') ? 'is-invalid' : '' ?>" 
                                  id="specifications" 
                                  name="specifications" 
                                  rows="3"
                                  placeholder="CPU: 2 cores, RAM: 4GB, Storage: 80GB SSD"><?= e(old('specifications', $server['specifications'])) ?></textarea>
                        <?php if (has_error('specifications')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('specifications')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control <?= has_error('notes') ? 'is-invalid' : '' ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4"
                                  placeholder="Additional notes about the server"><?= e(old('notes', $server['notes'])) ?></textarea>
                        <?php if (has_error('notes')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('notes')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= url('/servers/' . $server['id']) ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Server
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-1"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> Editing Server</h6>
                    <ul class="mb-0 small">
                        <li>Server name and type are required</li>
                        <li>IP address helps with server management</li>
                        <li>Auto renewal prevents service interruption</li>
                        <li>Status affects how the server appears in lists</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Renewal Alerts</h6>
                    <p class="mb-0 small">
                        The system will automatically send alerts before 
                        the server expires to prevent service interruption.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // IP address validation
    const ipAddressField = document.getElementById('ip_address');
    if (ipAddressField) {
        ipAddressField.addEventListener('blur', function() {
            const ip = this.value.trim();
            if (ip) {
                const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                if (!ipRegex.test(ip)) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            }
        });
    }

    // Form validation
    const serverForm = document.getElementById('serverForm');
    if (serverForm) {
        serverForm.addEventListener('submit', function(e) {
            let isValid = true;

            // Check required fields
            const requiredFields = ['client_id', 'server_name', 'server_type', 'purchase_date', 'expiry_date'];
            requiredFields.forEach(function(field) {
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    const value = fieldElement.value.trim();
                    if (!value) {
                        fieldElement.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        fieldElement.classList.remove('is-invalid');
                    }
                }
            });

            // Validate dates
            const purchaseDateField = document.getElementById('purchase_date');
            const expiryDateField = document.getElementById('expiry_date');

            if (purchaseDateField && expiryDateField) {
                const purchaseDate = new Date(purchaseDateField.value);
                const expiryDate = new Date(expiryDateField.value);

                if (expiryDate <= purchaseDate) {
                    expiryDateField.classList.add('is-invalid');
                    isValid = false;
                    if (typeof ClientManager !== 'undefined') {
                        ClientManager.showAlert('Expiry date must be after purchase date.', 'error');
                    } else {
                        alert('Expiry date must be after purchase date.');
                    }
                }
            }

            if (!isValid) {
                e.preventDefault();
                if (typeof ClientManager !== 'undefined') {
                    ClientManager.showAlert('Please correct the errors below.', 'error');
                } else {
                    alert('Please correct the errors below.');
                }
            }
        });
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
