<?php
/**
 * Report Controller
 */

class ReportController extends Controller
{
    private $clientModel;
    private $domainModel;
    private $serverModel;
    private $mediatorModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->clientModel = new Client();
        $this->domainModel = new Domain();
        $this->serverModel = new Server();
        $this->mediatorModel = new Mediator();
    }
    
    /**
     * Reports dashboard
     */
    public function index()
    {
        $stats = [
            'clients' => $this->clientModel->getStats(),
            'domains' => $this->domainModel->getStats(),
            'servers' => $this->serverModel->getStats(),
            'mediators' => $this->mediatorModel->getStats()
        ];
        
        echo $this->view('reports/index', [
            'title' => 'Reports & Analytics',
            'stats' => $stats,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Domain expiry report
     */
    public function domainExpiry()
    {
        $days = $_GET['days'] ?? 30;
        $format = $_GET['format'] ?? 'html';
        
        $expiringDomains = $this->domainModel->getExpiringDomains($days);
        $expiredDomains = $this->domainModel->getExpiredDomains();
        
        if ($format === 'csv') {
            $this->exportDomainExpiryCSV($expiringDomains, $expiredDomains, $days);
            return;
        }
        
        echo $this->view('reports/domain-expiry', [
            'title' => 'Domain Expiry Report',
            'expiring_domains' => $expiringDomains,
            'expired_domains' => $expiredDomains,
            'days' => $days
        ]);
    }
    
    /**
     * Server expiry report
     */
    public function serverExpiry()
    {
        $days = $_GET['days'] ?? 30;
        $format = $_GET['format'] ?? 'html';
        
        $expiringServers = $this->serverModel->getExpiringServers($days);
        $expiredServers = $this->serverModel->getExpiredServers();
        
        if ($format === 'csv') {
            $this->exportServerExpiryCSV($expiringServers, $expiredServers, $days);
            return;
        }
        
        echo $this->view('reports/server-expiry', [
            'title' => 'Server Expiry Report',
            'expiring_servers' => $expiringServers,
            'expired_servers' => $expiredServers,
            'days' => $days
        ]);
    }
    
    /**
     * Revenue report
     */
    public function revenue()
    {
        $startDate = $_GET['start_date'] ?? date('Y-01-01');
        $endDate = $_GET['end_date'] ?? date('Y-12-31');
        $format = $_GET['format'] ?? 'html';
        
        // Get revenue data
        $domainRevenue = $this->getDomainRevenue($startDate, $endDate);
        $serverRevenue = $this->getServerRevenue($startDate, $endDate);
        $clientRevenue = $this->getClientRevenue($startDate, $endDate);
        $mediatorCommissions = $this->getMediatorCommissions($startDate, $endDate);
        
        if ($format === 'csv') {
            $this->exportRevenueCSV($domainRevenue, $serverRevenue, $startDate, $endDate);
            return;
        }
        
        echo $this->view('reports/revenue', [
            'title' => 'Revenue Report',
            'domain_revenue' => $domainRevenue,
            'server_revenue' => $serverRevenue,
            'client_revenue' => $clientRevenue,
            'mediator_commissions' => $mediatorCommissions,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
    }
    
    /**
     * Client summary report
     */
    public function clientSummary()
    {
        $format = $_GET['format'] ?? 'html';
        
        $clients = $this->clientModel->all('name ASC');
        $clientSummaries = [];
        
        foreach ($clients as $client) {
            $summary = $this->clientModel->getRevenueSummary($client['id']);
            $domainCount = $this->domainModel->count(['client_id' => $client['id']]);
            $serverCount = $this->serverModel->count(['client_id' => $client['id']]);
            
            $clientSummaries[] = array_merge($client, [
                'domain_count' => $domainCount,
                'server_count' => $serverCount,
                'total_value' => $summary['total_costs']
            ]);
        }
        
        if ($format === 'csv') {
            $this->exportClientSummaryCSV($clientSummaries);
            return;
        }
        
        echo $this->view('reports/client-summary', [
            'title' => 'Client Summary Report',
            'clients' => $clientSummaries
        ]);
    }
    
    /**
     * Renewal calendar
     */
    public function renewalCalendar()
    {
        $year = $_GET['year'] ?? date('Y');
        $month = $_GET['month'] ?? date('m');
        
        $domainRenewals = $this->domainModel->getRenewalCalendar($year, $month);
        $serverRenewals = $this->serverModel->getRenewalCalendar($year, $month);
        
        echo $this->view('reports/renewal-calendar', [
            'title' => 'Renewal Calendar',
            'domain_renewals' => $domainRenewals,
            'server_renewals' => $serverRenewals,
            'year' => $year,
            'month' => $month
        ]);
    }
    
    /**
     * Get domain revenue data
     */
    private function getDomainRevenue($startDate, $endDate)
    {
        $sql = "SELECT 
                    SUM(renewal_cost) as total_revenue,
                    COUNT(*) as total_domains,
                    AVG(renewal_cost) as avg_cost,
                    registrar,
                    COUNT(*) as count
                FROM domains 
                WHERE purchase_date BETWEEN ? AND ?
                GROUP BY registrar
                ORDER BY total_revenue DESC";
        
        return $this->domainModel->query($sql, [$startDate, $endDate]);
    }
    
    /**
     * Get server revenue data
     */
    private function getServerRevenue($startDate, $endDate)
    {
        $sql = "SELECT 
                    SUM(renewal_cost) as total_revenue,
                    COUNT(*) as total_servers,
                    AVG(renewal_cost) as avg_cost,
                    provider,
                    server_type,
                    COUNT(*) as count
                FROM servers 
                WHERE purchase_date BETWEEN ? AND ?
                GROUP BY provider, server_type
                ORDER BY total_revenue DESC";
        
        return $this->serverModel->query($sql, [$startDate, $endDate]);
    }
    
    /**
     * Get client revenue data
     */
    private function getClientRevenue($startDate, $endDate)
    {
        $sql = "SELECT 
                    c.id,
                    c.name,
                    c.company,
                    COUNT(d.id) as domain_count,
                    COUNT(s.id) as server_count,
                    SUM(COALESCE(d.renewal_cost, 0)) + SUM(COALESCE(s.renewal_cost, 0)) as total_value
                FROM clients c
                LEFT JOIN domains d ON c.id = d.client_id AND d.purchase_date BETWEEN ? AND ?
                LEFT JOIN servers s ON c.id = s.client_id AND s.purchase_date BETWEEN ? AND ?
                WHERE c.is_active = 1
                GROUP BY c.id
                HAVING total_value > 0
                ORDER BY total_value DESC
                LIMIT 20";
        
        return $this->clientModel->query($sql, [$startDate, $endDate, $startDate, $endDate]);
    }
    
    /**
     * Get mediator commissions
     */
    private function getMediatorCommissions($startDate, $endDate)
    {
        $mediators = $this->mediatorModel->getActiveMediators();
        $commissions = [];
        
        foreach ($mediators as $mediator) {
            $report = $this->mediatorModel->getCommissionReport($mediator['id'], $startDate, $endDate);
            if ($report && $report['summary']['total_value'] > 0) {
                $commissions[] = [
                    'mediator' => $mediator,
                    'summary' => $report['summary']
                ];
            }
        }
        
        return $commissions;
    }
    
    /**
     * Export domain expiry to CSV
     */
    private function exportDomainExpiryCSV($expiringDomains, $expiredDomains, $days)
    {
        $filename = 'domain_expiry_report_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        fputcsv($output, ['Domain Expiry Report - Generated on ' . date('Y-m-d H:i:s')]);
        fputcsv($output, []);
        
        // Expiring domains
        fputcsv($output, ["Domains Expiring in Next {$days} Days"]);
        fputcsv($output, ['Domain Name', 'Client', 'Expiry Date', 'Days Until Expiry', 'Renewal Cost']);
        
        foreach ($expiringDomains as $domain) {
            fputcsv($output, [
                $domain['domain_name'],
                $domain['client_name'],
                $domain['expiry_date'],
                $domain['days_until_expiry'],
                '$' . number_format($domain['renewal_cost'], 2)
            ]);
        }
        
        fputcsv($output, []);
        
        // Expired domains
        fputcsv($output, ['Expired Domains']);
        fputcsv($output, ['Domain Name', 'Client', 'Expiry Date', 'Days Expired', 'Renewal Cost']);
        
        foreach ($expiredDomains as $domain) {
            fputcsv($output, [
                $domain['domain_name'],
                $domain['client_name'],
                $domain['expiry_date'],
                $domain['days_expired'],
                '$' . number_format($domain['renewal_cost'], 2)
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export server expiry to CSV
     */
    private function exportServerExpiryCSV($expiringServers, $expiredServers, $days)
    {
        $filename = 'server_expiry_report_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        fputcsv($output, ['Server Expiry Report - Generated on ' . date('Y-m-d H:i:s')]);
        fputcsv($output, []);
        
        // Expiring servers
        fputcsv($output, ["Servers Expiring in Next {$days} Days"]);
        fputcsv($output, ['Server Name', 'Client', 'Type', 'Expiry Date', 'Days Until Expiry', 'Renewal Cost']);
        
        foreach ($expiringServers as $server) {
            fputcsv($output, [
                $server['server_name'],
                $server['client_name'],
                $server['server_type'],
                $server['expiry_date'],
                $server['days_until_expiry'],
                '$' . number_format($server['renewal_cost'], 2)
            ]);
        }
        
        fputcsv($output, []);
        
        // Expired servers
        fputcsv($output, ['Expired Servers']);
        fputcsv($output, ['Server Name', 'Client', 'Type', 'Expiry Date', 'Days Expired', 'Renewal Cost']);
        
        foreach ($expiredServers as $server) {
            fputcsv($output, [
                $server['server_name'],
                $server['client_name'],
                $server['server_type'],
                $server['expiry_date'],
                $server['days_expired'],
                '$' . number_format($server['renewal_cost'], 2)
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * Export revenue to CSV
     */
    private function exportRevenueCSV($domainRevenue, $serverRevenue, $startDate, $endDate)
    {
        $filename = 'revenue_report_' . $startDate . '_to_' . $endDate . '.csv';

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        $output = fopen('php://output', 'w');

        fputcsv($output, ['Revenue Report - ' . $startDate . ' to ' . $endDate]);
        fputcsv($output, []);

        // Domain revenue
        fputcsv($output, ['Domain Revenue by Registrar']);
        fputcsv($output, ['Registrar', 'Total Revenue', 'Domain Count', 'Average Cost']);

        foreach ($domainRevenue as $item) {
            fputcsv($output, [
                $item['registrar'] ?: 'Unknown',
                '$' . number_format($item['total_revenue'], 2),
                $item['count'],
                '$' . number_format($item['avg_cost'], 2)
            ]);
        }

        fputcsv($output, []);

        // Server revenue
        fputcsv($output, ['Server Revenue by Provider']);
        fputcsv($output, ['Provider', 'Type', 'Total Revenue', 'Server Count', 'Average Cost']);

        foreach ($serverRevenue as $item) {
            fputcsv($output, [
                $item['provider'] ?: 'Unknown',
                $item['server_type'],
                '$' . number_format($item['total_revenue'], 2),
                $item['count'],
                '$' . number_format($item['avg_cost'], 2)
            ]);
        }

        fclose($output);
        exit;
    }

    /**
     * Export client summary to CSV
     */
    private function exportClientSummaryCSV($clients)
    {
        $filename = 'client_summary_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        $output = fopen('php://output', 'w');

        fputcsv($output, ['Client Summary Report - Generated on ' . date('Y-m-d H:i:s')]);
        fputcsv($output, []);
        fputcsv($output, ['Client Name', 'Company', 'Email', 'Phone', 'Domains', 'Servers', 'Total Value', 'Status']);

        foreach ($clients as $client) {
            fputcsv($output, [
                $client['name'],
                $client['company'],
                $client['email'],
                $client['phone'],
                $client['domain_count'],
                $client['server_count'],
                '$' . number_format($client['total_value'], 2),
                $client['is_active'] ? 'Active' : 'Inactive'
            ]);
        }

        fclose($output);
        exit;
    }
}
