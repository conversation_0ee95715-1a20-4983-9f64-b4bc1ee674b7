<?php
/**
 * Mediator Controller
 */

class MediatorController extends Controller
{
    private $mediatorModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->mediatorModel = new Mediator();
    }
    
    /**
     * Display mediators list
     */
    public function index()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $filter = $_GET['filter'] ?? '';
        
        if ($search) {
            $mediators = $this->mediatorModel->searchMediators($search);
            $pagination = null;
        } else {
            $conditions = [];
            if ($filter === 'active') {
                $conditions['is_active'] = 1;
            } elseif ($filter === 'inactive') {
                $conditions['is_active'] = 0;
            }
            
            $result = $this->mediatorModel->paginate($page, 20, $conditions, 'name ASC');
            $mediators = $result['data'];
            $pagination = $result['pagination'];
        }
        
        echo $this->view('mediators/index', [
            'title' => 'Mediators',
            'mediators' => $mediators,
            'pagination' => $pagination,
            'search' => $search,
            'filter' => $filter,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show create mediator form
     */
    public function create()
    {
        echo $this->view('mediators/create', [
            'title' => 'Add New Mediator',
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Store new mediator
     */
    public function store()
    {
        $this->checkCsrfToken();
        
        $rules = [
            'name' => 'required|max:100',
            'email' => 'email|max:100',
            'phone' => 'max:20',
            'company' => 'max:100',
            'commission_rate' => 'numeric'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/mediators/create');
        }
        
        $data = [
            'name' => $_POST['name'],
            'email' => $_POST['email'] ?? null,
            'phone' => $_POST['phone'] ?? null,
            'address' => $_POST['address'] ?? null,
            'company' => $_POST['company'] ?? null,
            'commission_rate' => $_POST['commission_rate'] ?? 0,
            'notes' => $_POST['notes'] ?? null,
            'is_active' => 1
        ];
        
        $mediatorId = $this->mediatorModel->create($data);
        
        if ($mediatorId) {
            $this->setFlashMessage('Mediator created successfully!', 'success');
            $this->redirect('/mediators/' . $mediatorId);
        } else {
            $this->setFlashMessage('Failed to create mediator.', 'error');
            $this->redirect('/mediators/create');
        }
    }
    
    /**
     * Show mediator details
     */
    public function show($id)
    {
        $mediator = $this->mediatorModel->getMediatorWithSummary($id);
        
        if (!$mediator) {
            $this->setFlashMessage('Mediator not found.', 'error');
            $this->redirect('/mediators');
        }
        
        echo $this->view('mediators/show', [
            'title' => 'Mediator Details - ' . $mediator['name'],
            'mediator' => $mediator,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show edit mediator form
     */
    public function edit($id)
    {
        $mediator = $this->mediatorModel->find($id);
        
        if (!$mediator) {
            $this->setFlashMessage('Mediator not found.', 'error');
            $this->redirect('/mediators');
        }
        
        echo $this->view('mediators/edit', [
            'title' => 'Edit Mediator - ' . $mediator['name'],
            'mediator' => $mediator,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update mediator
     */
    public function update($id)
    {
        $this->checkCsrfToken();
        
        $mediator = $this->mediatorModel->find($id);
        if (!$mediator) {
            $this->setFlashMessage('Mediator not found.', 'error');
            $this->redirect('/mediators');
        }
        
        $rules = [
            'name' => 'required|max:100',
            'email' => 'email|max:100',
            'phone' => 'max:20',
            'company' => 'max:100',
            'commission_rate' => 'numeric'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/mediators/' . $id . '/edit');
        }
        
        $data = [
            'name' => $_POST['name'],
            'email' => $_POST['email'] ?? null,
            'phone' => $_POST['phone'] ?? null,
            'address' => $_POST['address'] ?? null,
            'company' => $_POST['company'] ?? null,
            'commission_rate' => $_POST['commission_rate'] ?? 0,
            'notes' => $_POST['notes'] ?? null,
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        if ($this->mediatorModel->update($id, $data)) {
            $this->setFlashMessage('Mediator updated successfully!', 'success');
            $this->redirect('/mediators/' . $id);
        } else {
            $this->setFlashMessage('Failed to update mediator.', 'error');
            $this->redirect('/mediators/' . $id . '/edit');
        }
    }
    
    /**
     * Delete mediator
     */
    public function delete($id)
    {
        $this->checkCsrfToken();
        
        $mediator = $this->mediatorModel->find($id);
        if (!$mediator) {
            $this->setFlashMessage('Mediator not found.', 'error');
            $this->redirect('/mediators');
        }
        
        // Check if mediator has associated domains or servers
        $domainModel = new Domain();
        $serverModel = new Server();
        
        $domainCount = $domainModel->count(['mediator_id' => $id]);
        $serverCount = $serverModel->count(['mediator_id' => $id]);
        
        if ($domainCount > 0 || $serverCount > 0) {
            $this->setFlashMessage('Cannot delete mediator with associated transactions.', 'error');
            $this->redirect('/mediators/' . $id);
        }
        
        if ($this->mediatorModel->delete($id)) {
            $this->setFlashMessage('Mediator deleted successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to delete mediator.', 'error');
        }
        
        $this->redirect('/mediators');
    }
    
    /**
     * Generate commission report
     */
    public function commissionReport($id)
    {
        $startDate = $_GET['start_date'] ?? date('Y-01-01');
        $endDate = $_GET['end_date'] ?? date('Y-12-31');
        
        $report = $this->mediatorModel->getCommissionReport($id, $startDate, $endDate);
        
        if (!$report) {
            $this->setFlashMessage('Mediator not found.', 'error');
            $this->redirect('/mediators');
        }
        
        echo $this->view('mediators/commission-report', [
            'title' => 'Commission Report - ' . $report['mediator']['name'],
            'report' => $report,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
    }
    
    /**
     * Export mediators data
     */
    public function export()
    {
        $format = $_GET['format'] ?? 'csv';
        $mediators = $this->mediatorModel->getTopMediators(100);
        
        if ($format === 'csv') {
            $this->exportCSV($mediators);
        } else {
            $this->setFlashMessage('Invalid export format.', 'error');
            $this->redirect('/mediators');
        }
    }
    
    /**
     * Export mediators to CSV
     */
    private function exportCSV($mediators)
    {
        $filename = 'mediators_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'Name', 'Email', 'Phone', 'Company', 'Commission Rate', 
            'Total Transactions', 'Total Value', 'Commission Earned', 'Status'
        ]);
        
        // CSV data
        foreach ($mediators as $mediator) {
            fputcsv($output, [
                $mediator['name'],
                $mediator['email'],
                $mediator['phone'],
                $mediator['company'],
                $mediator['commission_rate'] . '%',
                $mediator['total_transactions'] ?? 0,
                '$' . number_format($mediator['total_value'] ?? 0, 2),
                '$' . number_format($mediator['commission_earned'] ?? 0, 2),
                $mediator['is_active'] ? 'Active' : 'Inactive'
            ]);
        }
        
        fclose($output);
        exit;
    }
}
