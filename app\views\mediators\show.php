<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-handshake me-2 text-primary"></i>
                <?= e($mediator['name']) ?>
            </h1>
            <div>
                <a href="<?= url('/mediators/' . $mediator['id'] . '/edit') ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i>
                    Edit Mediator
                </a>
                <a href="<?= url('/mediators') ?>" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Mediators
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Mediator Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Full Name</label>
                            <p class="form-control-plaintext"><?= e($mediator['name']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Company</label>
                            <p class="form-control-plaintext"><?= e($mediator['company']) ?: '<span class="text-muted">Not specified</span>' ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="form-control-plaintext">
                                <?php if ($mediator['email']): ?>
                                    <a href="mailto:<?= e($mediator['email']) ?>"><?= e($mediator['email']) ?></a>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Phone</label>
                            <p class="form-control-plaintext">
                                <?php if ($mediator['phone']): ?>
                                    <a href="tel:<?= e($mediator['phone']) ?>"><?= e($mediator['phone']) ?></a>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Commission Rate</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info fs-6"><?= number_format($mediator['commission_rate'], 1) ?>%</span>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Status</label>
                            <p class="form-control-plaintext">
                                <?php if ($mediator['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Inactive</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <?php if ($mediator['address']): ?>
                <div class="mb-3">
                    <label class="form-label text-muted">Address</label>
                    <p class="form-control-plaintext"><?= nl2br(e($mediator['address'])) ?></p>
                </div>
                <?php endif; ?>
                
                <?php if ($mediator['notes']): ?>
                <div class="mb-3">
                    <label class="form-label text-muted">Notes</label>
                    <p class="form-control-plaintext"><?= nl2br(e($mediator['notes'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-chart-line me-1"></i>
                    Commission Summary
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <h4 class="text-success"><?= format_currency($mediator['total_commission'] ?? 0) ?></h4>
                        <small class="text-muted">Total Commission Earned</small>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-primary"><?= $mediator['total_transactions'] ?? 0 ?></h6>
                                <small class="text-muted">Transactions</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-info"><?= format_currency($mediator['total_value'] ?? 0) ?></h6>
                            <small class="text-muted">Total Value</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <a href="<?= url('/mediators/' . $mediator['id'] . '/commission-report') ?>" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-file-alt me-1"></i>
                        View Commission Report
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-1"></i>
                    Details
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <small class="text-muted d-block">Member Since</small>
                        <strong><?= format_date($mediator['created_at'], 'M j, Y') ?></strong>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="<?= url('/mediators/' . $mediator['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>
                        Edit Information
                    </a>
                    
                    <?php if ($mediator['is_active']): ?>
                        <form method="POST" action="<?= url('/mediators/' . $mediator['id'] . '/toggle-status') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn btn-outline-warning btn-sm w-100" 
                                    onclick="return confirm('Are you sure you want to deactivate this mediator?')">
                                <i class="fas fa-pause me-1"></i>
                                Deactivate
                            </button>
                        </form>
                    <?php else: ?>
                        <form method="POST" action="<?= url('/mediators/' . $mediator['id'] . '/toggle-status') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                <i class="fas fa-play me-1"></i>
                                Activate
                            </button>
                        </form>
                    <?php endif; ?>
                    
                    <button type="button" class="btn btn-outline-danger btn-sm" 
                            onclick="deleteMediator(<?= $mediator['id'] ?>, '<?= e($mediator['name']) ?>')">
                        <i class="fas fa-trash me-1"></i>
                        Delete Mediator
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete mediator <strong id="mediatorName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <?= method_field('DELETE') ?>
                    <button type="submit" class="btn btn-danger">Delete Mediator</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteMediator(id, name) {
    document.getElementById('mediatorName').textContent = name;
    document.getElementById('deleteForm').action = '<?= url('/mediators/') ?>' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
