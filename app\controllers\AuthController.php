<?php
/**
 * Authentication Controller
 */

class AuthController extends Controller
{
    /**
     * Show login form
     */
    public function login()
    {
        // Redirect if already logged in
        if (Auth::check()) {
            $this->redirect('/dashboard');
        }
        
        echo $this->view('auth/login', [
            'title' => 'Login',
            'flash_messages' => $this->getFlashMessages(),
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Handle login attempt
     */
    public function authenticate()
    {
        // Check CSRF token
        $this->checkCsrfToken();
        
        // Get input data
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        // Validate input
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/login');
        }
        
        // Check if account is locked
        if (Auth::getLockoutTime($username) > 0) {
            $remainingTime = Auth::getLockoutTime($username);
            $minutes = ceil($remainingTime / 60);
            $this->setFlashMessage("Account is locked. Please try again in {$minutes} minutes.", 'error');
            $this->redirect('/login');
        }
        
        // Attempt authentication
        $credentials = [
            'username' => $username,
            'password' => $password
        ];
        
        if (Auth::attempt($credentials)) {
            // Set remember me cookie if requested
            if ($remember) {
                $this->setRememberMeCookie();
            }
            
            // Log successful login
            Helper::log("User {$username} logged in successfully", 'info', [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            // Redirect to intended page or dashboard
            $redirectTo = $_SESSION['intended_url'] ?? '/dashboard';
            unset($_SESSION['intended_url']);
            
            $this->setFlashMessage('Welcome back!', 'success');
            $this->redirect($redirectTo);
        } else {
            // Log failed attempt
            Helper::log("Failed login attempt for {$username}", 'warning', [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            $this->setFlashMessage('Invalid username or password.', 'error');
            $this->redirect('/login');
        }
    }
    
    /**
     * Logout user
     */
    public function logout()
    {
        $user = Auth::user();
        
        if ($user) {
            Helper::log("User {$user['username']} logged out", 'info');
        }
        
        // Clear remember me cookie
        $this->clearRememberMeCookie();
        
        // Logout
        Auth::logout();
        
        $this->setFlashMessage('You have been logged out successfully.', 'info');
        $this->redirect('/login');
    }
    
    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie()
    {
        $user = Auth::user();
        if (!$user) return;
        
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (30 * 24 * 60 * 60); // 30 days
        
        // Store token in database (you might want to create a remember_tokens table)
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
        $stmt->execute([$token, $user['id']]);
        
        // Set cookie
        setcookie('remember_token', $token, $expiry, '/', '', false, true);
    }
    
    /**
     * Clear remember me cookie
     */
    private function clearRememberMeCookie()
    {
        if (isset($_COOKIE['remember_token'])) {
            // Clear from database
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("UPDATE users SET remember_token = NULL WHERE remember_token = ?");
            $stmt->execute([$_COOKIE['remember_token']]);
            
            // Clear cookie
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
    }
    
    /**
     * Check remember me token (call this in your bootstrap)
     */
    public static function checkRememberMe()
    {
        if (Auth::check() || !isset($_COOKIE['remember_token'])) {
            return;
        }
        
        $token = $_COOKIE['remember_token'];
        
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT * FROM users WHERE remember_token = ? AND is_active = 1");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if ($user) {
            Auth::login($user);
        } else {
            // Invalid token, clear cookie
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
    }
}
