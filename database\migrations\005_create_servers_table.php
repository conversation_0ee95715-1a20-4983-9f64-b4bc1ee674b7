<?php
$sql = "CREATE TABLE IF NOT EXISTS servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    mediator_id INT,
    server_name VARCHAR(100) NOT NULL,
    domain_id INT,
    provider VARCHAR(100),
    server_type ENUM('shared', 'vps', 'dedicated', 'cloud') DEFAULT 'shared',
    ip_address VARCHAR(45),
    purchase_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    current_cost DECIMAL(10,2),
    auto_renewal BOOLEAN DEFAULT FALSE,
    specifications TEXT,
    status ENUM('active', 'expired', 'suspended', 'terminated') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (mediator_id) REFERENCES mediators(id) ON DELETE SET NULL,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (domain_id) REFERENCES domains(id) ON DELETE SET NULL
)";

$this->connection->exec($sql);

// Rename renewal_cost to current_cost and add domain_id if the table already exists
try {
    $this->connection->exec("ALTER TABLE servers CHANGE renewal_cost current_cost DECIMAL(10,2)");
} catch (PDOException $e) {
    // Column might not exist or already renamed, ignore error
}

try {
    $this->connection->exec("ALTER TABLE servers ADD COLUMN domain_id INT AFTER server_name");
    $this->connection->exec("ALTER TABLE servers ADD FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE SET NULL");
} catch (PDOException $e) {
    // Column might already exist, ignore error
}
