<?php
/**
 * Authentication Class
 * Handles user authentication and authorization
 */

class Auth
{
    /**
     * Check if user is authenticated
     */
    public static function check()
    {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Get authenticated user
     */
    public static function user()
    {
        if (!self::check()) {
            return null;
        }
        
        // Get user from cache or database
        $cache = new Cache();
        $cacheKey = 'user_' . $_SESSION['user_id'];
        
        return $cache->remember($cacheKey, function() {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
            $stmt->execute([$_SESSION['user_id']]);
            return $stmt->fetch();
        }, 300); // Cache for 5 minutes
    }
    
    /**
     * Get user ID
     */
    public static function id()
    {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Attempt to authenticate user
     */
    public static function attempt($credentials)
    {
        $db = Database::getInstance()->getConnection();
        
        // Find user by username or email
        $stmt = $db->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
        $stmt->execute([$credentials['username'], $credentials['username']]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return false;
        }
        
        // Verify password
        if (!password_verify($credentials['password'], $user['password'])) {
            // Log failed attempt
            self::logFailedAttempt($credentials['username']);
            return false;
        }
        
        // Check if account is locked
        if (self::isAccountLocked($credentials['username'])) {
            return false;
        }
        
        // Login successful
        self::login($user);
        self::clearFailedAttempts($credentials['username']);
        
        return true;
    }
    
    /**
     * Login user
     */
    public static function login($user)
    {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['login_time'] = time();
        
        // Update last login
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // Clear user cache
        $cache = new Cache();
        $cache->forget('user_' . $user['id']);
        
        // Log successful login
        self::logActivity('login', $user['id']);
    }
    
    /**
     * Logout user
     */
    public static function logout()
    {
        $userId = self::id();
        
        // Log logout
        if ($userId) {
            self::logActivity('logout', $userId);
        }
        
        // Clear session
        session_unset();
        session_destroy();
        
        // Start new session
        session_start();
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin()
    {
        $user = self::user();
        return $user && $user['role'] === 'admin';
    }
    
    /**
     * Check if user is staff
     */
    public static function isStaff()
    {
        $user = self::user();
        return $user && $user['role'] === 'staff';
    }
    
    /**
     * Check if user has permission
     */
    public static function can($permission)
    {
        $user = self::user();
        
        if (!$user) {
            return false;
        }
        
        // Admin has all permissions
        if ($user['role'] === 'admin') {
            return true;
        }
        
        // Define staff permissions
        $staffPermissions = [
            'view_clients',
            'create_clients',
            'edit_clients',
            'view_domains',
            'create_domains',
            'edit_domains',
            'view_servers',
            'create_servers',
            'edit_servers',
            'view_mediators',
            'view_reports'
        ];
        
        return in_array($permission, $staffPermissions);
    }
    
    /**
     * Log failed login attempt
     */
    private static function logFailedAttempt($username)
    {
        $key = 'failed_attempts_' . md5($username);
        $attempts = $_SESSION[$key] ?? 0;
        $_SESSION[$key] = $attempts + 1;
        $_SESSION[$key . '_time'] = time();
    }
    
    /**
     * Clear failed login attempts
     */
    private static function clearFailedAttempts($username)
    {
        $key = 'failed_attempts_' . md5($username);
        unset($_SESSION[$key]);
        unset($_SESSION[$key . '_time']);
    }
    
    /**
     * Check if account is locked
     */
    private static function isAccountLocked($username)
    {
        $config = include CONFIG_PATH . '/config.php';
        $maxAttempts = $config['security']['max_login_attempts'];
        $lockoutDuration = $config['security']['lockout_duration'];
        
        $key = 'failed_attempts_' . md5($username);
        $attempts = $_SESSION[$key] ?? 0;
        $lastAttempt = $_SESSION[$key . '_time'] ?? 0;
        
        if ($attempts >= $maxAttempts) {
            if (time() - $lastAttempt < $lockoutDuration) {
                return true;
            } else {
                // Lockout period expired, clear attempts
                self::clearFailedAttempts($username);
            }
        }
        
        return false;
    }
    
    /**
     * Get remaining lockout time
     */
    public static function getLockoutTime($username)
    {
        $config = include CONFIG_PATH . '/config.php';
        $lockoutDuration = $config['security']['lockout_duration'];
        
        $key = 'failed_attempts_' . md5($username);
        $lastAttempt = $_SESSION[$key . '_time'] ?? 0;
        
        $remaining = $lockoutDuration - (time() - $lastAttempt);
        return max(0, $remaining);
    }
    
    /**
     * Log user activity
     */
    private static function logActivity($action, $userId)
    {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("INSERT INTO audit_logs (user_id, action, table_name, record_id, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                $userId,
                $action,
                'users',
                $userId,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            // Silently fail - don't break authentication for logging issues
            error_log("Failed to log activity: " . $e->getMessage());
        }
    }
    
    /**
     * Generate secure password
     */
    public static function generatePassword($length = 12)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        return substr(str_shuffle($chars), 0, $length);
    }
    
    /**
     * Hash password
     */
    public static function hashPassword($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
}
