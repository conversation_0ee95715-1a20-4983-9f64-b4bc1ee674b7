<?php
/**
 * Client Controller
 */

class ClientController extends Controller
{
    private $clientModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->clientModel = new Client();
    }
    
    /**
     * Display clients list
     */
    public function index()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $filter = $_GET['filter'] ?? '';
        
        // Get clients with pagination
        if ($search) {
            $clients = $this->clientModel->searchClients($search);
            $pagination = null;
        } else {
            $conditions = [];
            if ($filter === 'active') {
                $conditions['is_active'] = 1;
            } elseif ($filter === 'inactive') {
                $conditions['is_active'] = 0;
            }

            $clients = $this->clientModel->getClientsWithDetails($conditions, 'name ASC');
            $pagination = null;
        }
        
        echo $this->view('clients/index', [
            'title' => 'Clients',
            'clients' => $clients,
            'pagination' => $pagination,
            'search' => $search,
            'filter' => $filter,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show create client form
     */
    public function create()
    {
        echo $this->view('clients/create', [
            'title' => 'Add New Client',
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Store new client
     */
    public function store()
    {
        $this->checkCsrfToken();
        
        $rules = [
            'name' => 'required|max:100',
            'email' => 'email|max:100',
            'phone' => 'max:20',
            'company' => 'max:100'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/clients/create');
        }
        
        $data = [
            'name' => $_POST['name'],
            'email' => $_POST['email'] ?? null,
            'phone' => $_POST['phone'] ?? null,
            'address' => $_POST['address'] ?? null,
            'company' => $_POST['company'] ?? null,
            'notes' => $_POST['notes'] ?? null,
            'is_active' => 1
        ];
        
        $clientId = $this->clientModel->create($data);
        
        if ($clientId) {
            $this->setFlashMessage('Client created successfully!', 'success');
            $this->redirect('/clients/' . $clientId);
        } else {
            $this->setFlashMessage('Failed to create client.', 'error');
            $this->redirect('/clients/create');
        }
    }
    
    /**
     * Show client details
     */
    public function show($id)
    {
        $client = $this->clientModel->getClientWithServices($id);
        
        if (!$client) {
            $this->setFlashMessage('Client not found.', 'error');
            $this->redirect('/clients');
        }
        
        echo $this->view('clients/show', [
            'title' => 'Client Details - ' . $client['name'],
            'client' => $client,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show edit client form
     */
    public function edit($id)
    {
        $client = $this->clientModel->find($id);
        
        if (!$client) {
            $this->setFlashMessage('Client not found.', 'error');
            $this->redirect('/clients');
        }
        
        echo $this->view('clients/edit', [
            'title' => 'Edit Client - ' . $client['name'],
            'client' => $client,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update client
     */
    public function update($id)
    {
        $this->checkCsrfToken();
        
        $client = $this->clientModel->find($id);
        if (!$client) {
            $this->setFlashMessage('Client not found.', 'error');
            $this->redirect('/clients');
        }
        
        $rules = [
            'name' => 'required|max:100',
            'email' => 'email|max:100',
            'phone' => 'max:20',
            'company' => 'max:100'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/clients/' . $id . '/edit');
        }
        
        $data = [
            'name' => $_POST['name'],
            'email' => $_POST['email'] ?? null,
            'phone' => $_POST['phone'] ?? null,
            'address' => $_POST['address'] ?? null,
            'company' => $_POST['company'] ?? null,
            'notes' => $_POST['notes'] ?? null,
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        if ($this->clientModel->update($id, $data)) {
            $this->setFlashMessage('Client updated successfully!', 'success');
            $this->redirect('/clients/' . $id);
        } else {
            $this->setFlashMessage('Failed to update client.', 'error');
            $this->redirect('/clients/' . $id . '/edit');
        }
    }
    
    /**
     * Delete client
     */
    public function delete($id)
    {
        $this->checkCsrfToken();
        
        $client = $this->clientModel->find($id);
        if (!$client) {
            $this->setFlashMessage('Client not found.', 'error');
            $this->redirect('/clients');
        }
        
        // Check if client has associated domains or servers
        $domainModel = new Domain();
        $serverModel = new Server();
        
        $domainCount = $domainModel->count(['client_id' => $id]);
        $serverCount = $serverModel->count(['client_id' => $id]);
        
        if ($domainCount > 0 || $serverCount > 0) {
            $this->setFlashMessage('Cannot delete client with associated domains or servers.', 'error');
            $this->redirect('/clients/' . $id);
        }
        
        if ($this->clientModel->delete($id)) {
            $this->setFlashMessage('Client deleted successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to delete client.', 'error');
        }
        
        $this->redirect('/clients');
    }
    
    /**
     * Export clients data
     */
    public function export()
    {
        $format = $_GET['format'] ?? 'csv';
        $clients = $this->clientModel->all('name ASC');
        
        if ($format === 'csv') {
            $this->exportCSV($clients);
        } else {
            $this->setFlashMessage('Invalid export format.', 'error');
            $this->redirect('/clients');
        }
    }
    
    /**
     * Export clients to CSV
     */
    private function exportCSV($clients)
    {
        $filename = 'clients_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'ID', 'Name', 'Email', 'Phone', 'Company', 'Address', 'Status', 'Created At'
        ]);
        
        // CSV data
        foreach ($clients as $client) {
            fputcsv($output, [
                $client['id'],
                $client['name'],
                $client['email'],
                $client['phone'],
                $client['company'],
                $client['address'],
                $client['is_active'] ? 'Active' : 'Inactive',
                $client['created_at']
            ]);
        }
        
        fclose($output);
        exit;
    }
}
