<?php
/**
 * Mediator Model
 */

class Mediator extends Model
{
    protected $table = 'mediators';
    protected $fillable = [
        'name', 'email', 'phone', 'address', 'company', 'commission_rate', 'notes', 'is_active'
    ];
    
    /**
     * Get active mediators
     */
    public function getActiveMediators()
    {
        return $this->where(['is_active' => 1], 'name ASC');
    }
    
    /**
     * Get mediator with transaction summary
     */
    public function getMediatorWithSummary($id)
    {
        $mediator = $this->find($id);
        
        if (!$mediator) {
            return null;
        }
        
        // Get domain transactions
        $domainModel = new Domain();
        $domains = $domainModel->where(['mediator_id' => $id]);
        
        // Get server transactions
        $serverModel = new Server();
        $servers = $serverModel->where(['mediator_id' => $id]);
        
        // Calculate totals
        $totalDomainCost = array_sum(array_column($domains, 'current_cost'));
        $totalServerCost = array_sum(array_column($servers, 'current_cost'));
        $totalTransactions = $totalDomainCost + $totalServerCost;
        $commission = $totalTransactions * ($mediator['commission_rate'] / 100);
        
        $mediator['summary'] = [
            'total_domains' => count($domains),
            'total_servers' => count($servers),
            'total_domain_cost' => $totalDomainCost,
            'total_server_cost' => $totalServerCost,
            'total_transactions' => $totalTransactions,
            'commission_earned' => $commission
        ];
        
        $mediator['domains'] = $domains;
        $mediator['servers'] = $servers;
        
        return $mediator;
    }
    
    /**
     * Search mediators
     */
    public function searchMediators($query)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (name LIKE ? OR email LIKE ? OR phone LIKE ? OR company LIKE ?) 
                AND is_active = 1 
                ORDER BY name ASC";
        
        $searchTerm = "%{$query}%";
        return $this->query($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * Get mediator statistics
     */
    public function getStats()
    {
        $stats = [
            'total_mediators' => $this->count(['is_active' => 1]),
            'total_transactions' => 0,
            'total_commission' => 0
        ];
        
        // Get transaction totals
        $sql = "SELECT
                    COUNT(d.id) + COUNT(s.id) as total_transactions,
                    SUM(COALESCE(d.current_cost, 0)) + SUM(COALESCE(s.current_cost, 0)) as total_value
                FROM mediators m
                LEFT JOIN domains d ON m.id = d.mediator_id AND d.status = 'active'
                LEFT JOIN servers s ON m.id = s.mediator_id AND s.status = 'active'
                WHERE m.is_active = 1";
        
        $result = $this->query($sql);
        if (!empty($result)) {
            $stats['total_transactions'] = $result[0]['total_transactions'] ?? 0;
            $totalValue = $result[0]['total_value'] ?? 0;
            
            // Calculate average commission (assuming 5% average)
            $stats['total_commission'] = $totalValue * 0.05;
        }
        
        return $stats;
    }
    
    /**
     * Get top mediators by transaction volume
     */
    public function getTopMediators($limit = 10)
    {
        $sql = "SELECT m.*,
                       COUNT(d.id) + COUNT(s.id) as total_transactions,
                       SUM(COALESCE(d.current_cost, 0)) + SUM(COALESCE(s.current_cost, 0)) as total_value,
                       (SUM(COALESCE(d.current_cost, 0)) + SUM(COALESCE(s.current_cost, 0))) * (m.commission_rate / 100) as commission_earned
                FROM mediators m
                LEFT JOIN domains d ON m.id = d.mediator_id AND d.status = 'active'
                LEFT JOIN servers s ON m.id = s.mediator_id AND s.status = 'active'
                WHERE m.is_active = 1
                GROUP BY m.id
                ORDER BY total_value DESC
                LIMIT ?";
        
        return $this->query($sql, [$limit]);
    }
    
    /**
     * Get mediator commission report
     */
    public function getCommissionReport($mediatorId, $startDate = null, $endDate = null)
    {
        $startDate = $startDate ?: date('Y-01-01');
        $endDate = $endDate ?: date('Y-12-31');
        
        $mediator = $this->find($mediatorId);
        if (!$mediator) {
            return null;
        }
        
        // Get domains in date range
        $domainSql = "SELECT 'domain' as type, domain_name as name, purchase_date, current_cost
                      FROM domains
                      WHERE mediator_id = ? AND purchase_date BETWEEN ? AND ?";

        $domains = $this->query($domainSql, [$mediatorId, $startDate, $endDate]);

        // Get servers in date range
        $serverSql = "SELECT 'server' as type, server_name as name, purchase_date, current_cost
                      FROM servers
                      WHERE mediator_id = ? AND purchase_date BETWEEN ? AND ?";

        $servers = $this->query($serverSql, [$mediatorId, $startDate, $endDate]);
        
        // Combine and calculate
        $transactions = array_merge($domains, $servers);
        $totalValue = array_sum(array_column($transactions, 'current_cost'));
        $commission = $totalValue * ($mediator['commission_rate'] / 100);
        
        return [
            'mediator' => $mediator,
            'transactions' => $transactions,
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => [
                'total_transactions' => count($transactions),
                'total_value' => $totalValue,
                'commission_rate' => $mediator['commission_rate'],
                'commission_earned' => $commission
            ]
        ];
    }
}
