<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-handshake me-2 text-primary"></i>
                Mediator Management
            </h1>
            <div>
                <a href="<?= url('/mediators/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add New Mediator
                </a>
                <div class="btn-group ms-2">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?= url('/mediators/export?format=csv') ?>">
                            <i class="fas fa-file-csv me-1"></i> Export CSV
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="Search mediators..." value="<?= e($search) ?>">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i>
            </button>
            <?php if ($search): ?>
                <a href="<?= url('/mediators') ?>" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-times"></i>
                </a>
            <?php endif; ?>
        </form>
    </div>
    <div class="col-md-6">
        <div class="btn-group w-100" role="group">
            <a href="<?= url('/mediators') ?>" 
               class="btn <?= $filter === '' ? 'btn-primary' : 'btn-outline-primary' ?>">
                All
            </a>
            <a href="<?= url('/mediators?filter=active') ?>" 
               class="btn <?= $filter === 'active' ? 'btn-primary' : 'btn-outline-primary' ?>">
                Active
            </a>
            <a href="<?= url('/mediators?filter=inactive') ?>" 
               class="btn <?= $filter === 'inactive' ? 'btn-primary' : 'btn-outline-primary' ?>">
                Inactive
            </a>
        </div>
    </div>
</div>

<!-- Mediators Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-1"></i>
                    Mediators List
                    <?php if ($search): ?>
                        <small class="text-muted">- Search results for "<?= e($search) ?>"</small>
                    <?php endif; ?>
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($mediators)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No mediators found</h5>
                        <p class="text-muted">
                            <?php if ($search): ?>
                                No mediators match your search criteria.
                            <?php else: ?>
                                Get started by adding your first mediator.
                            <?php endif; ?>
                        </p>
                        <?php if (!$search): ?>
                            <a href="<?= url('/mediators/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Add First Mediator
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Company</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Commission Rate</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($mediators as $mediator): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?= avatar($mediator, 32) ?>
                                            <div class="ms-2">
                                                <strong><?= e($mediator['name']) ?></strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= e($mediator['company']) ?: '<span class="text-muted">-</span>' ?></td>
                                    <td>
                                        <?php if ($mediator['email']): ?>
                                            <a href="mailto:<?= e($mediator['email']) ?>" class="text-decoration-none">
                                                <?= e($mediator['email']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($mediator['phone']): ?>
                                            <a href="tel:<?= e($mediator['phone']) ?>" class="text-decoration-none">
                                                <?= e($mediator['phone']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= number_format($mediator['commission_rate'], 1) ?>%
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($mediator['is_active']): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= format_date($mediator['created_at'], 'M j, Y') ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= url('/mediators/' . $mediator['id']) ?>" 
                                               class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= url('/mediators/' . $mediator['id'] . '/edit') ?>" 
                                               class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteMediator(<?= $mediator['id'] ?>, '<?= e($mediator['name']) ?>')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination && $pagination['total_pages'] > 1): ?>
                        <nav aria-label="Mediators pagination" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($pagination['has_previous']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= url('/mediators?page=' . ($pagination['current_page'] - 1) . ($search ? '&search=' . urlencode($search) : '') . ($filter ? '&filter=' . $filter : '')) ?>">
                                            Previous
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                                    <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                        <a class="page-link" href="<?= url('/mediators?page=' . $i . ($search ? '&search=' . urlencode($search) : '') . ($filter ? '&filter=' . $filter : '')) ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= url('/mediators?page=' . ($pagination['current_page'] + 1) . ($search ? '&search=' . urlencode($search) : '') . ($filter ? '&filter=' . $filter : '')) ?>">
                                            Next
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        
                        <div class="text-center text-muted">
                            <small>
                                Showing <?= $pagination['current_page'] * $pagination['per_page'] - $pagination['per_page'] + 1 ?> 
                                to <?= min($pagination['current_page'] * $pagination['per_page'], $pagination['total_items']) ?> 
                                of <?= $pagination['total_items'] ?> mediators
                            </small>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete mediator <strong id="mediatorName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <?= method_field('DELETE') ?>
                    <button type="submit" class="btn btn-danger">Delete Mediator</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteMediator(id, name) {
    document.getElementById('mediatorName').textContent = name;
    document.getElementById('deleteForm').action = '<?= url('/mediators/') ?>' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
