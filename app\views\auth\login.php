<?php
$bodyClass = 'login-page';
$content = ob_get_clean();
ob_start();
?>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="logo-container">
                <div class="logo-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h1 class="app-title"><?= e($config['app']['name']) ?></h1>
                <p class="app-subtitle">Sign in to your account</p>
            </div>
        </div>

        <div class="login-body">
                    
            <!-- Login Form -->
            <form method="POST" action="<?= url('/login') ?>" id="loginForm" class="login-form">
                <?= csrf_field() ?>

                <div class="form-group">
                    <label for="username" class="form-label">Username or Email</label>
                    <div class="input-wrapper">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text"
                               class="form-input <?= has_error('username') ? 'error' : '' ?>"
                               id="username"
                               name="username"
                               value="<?= e(old('username')) ?>"
                               placeholder="admin"
                               required
                               autofocus>
                        <?php if (has_error('username')): ?>
                            <div class="error-message">
                                <?= e(error('username')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-wrapper">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password"
                               class="form-input <?= has_error('password') ? 'error' : '' ?>"
                               id="password"
                               name="password"
                               placeholder="••••••••"
                               required>
                        <button class="password-toggle" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                        <?php if (has_error('password')): ?>
                            <div class="error-message">
                                <?= e(error('password')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="remember" name="remember" value="1">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </button>
            </form>

            <!-- Additional Links -->
            <div class="login-links">
                <a href="<?= url('/forgot-password') ?>" class="forgot-link">
                    Forgot your password? Reset it here
                </a>
            </div>
        </div>

        <!-- System Info -->
        <div class="login-footer">
            <div class="version-info">
                Version <?= e($config['app']['version']) ?> |
                <span id="systemStatus" class="status-online">
                    <i class="fas fa-circle"></i> System Online
                </span>
            </div>

            <!-- Environment Indicator -->
            <?php if (DEBUG_MODE): ?>
                <div class="dev-mode-indicator">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Development Mode</strong> - Debug information is enabled
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    .login-page {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .login-container {
        width: 100%;
        max-width: 400px;
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .login-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        padding: 40px 30px;
        text-align: center;
        color: white;
    }

    .logo-container {
        animation: fadeIn 0.8s ease-out 0.2s both;
    }

    .logo-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        backdrop-filter: blur(10px);
    }

    .logo-icon i {
        font-size: 2.5rem;
        color: white;
    }

    .app-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .app-subtitle {
        font-size: 0.95rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 300;
    }

    .login-body {
        padding: 40px 30px;
    }

    .login-form {
        animation: fadeIn 0.8s ease-out 0.4s both;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
        font-size: 0.9rem;
    }

    .input-wrapper {
        position: relative;
    }

    .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 1rem;
        z-index: 2;
    }

    .form-input {
        width: 100%;
        padding: 15px 15px 15px 45px;
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .form-input:focus {
        outline: none;
        border-color: #4facfe;
        background: white;
        box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    }

    .form-input.error {
        border-color: #dc3545;
        background: #fff5f5;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        transition: color 0.3s ease;
    }

    .password-toggle:hover {
        color: #4facfe;
    }

    .checkbox-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: 0.9rem;
        color: #666;
    }

    .checkbox-wrapper input[type="checkbox"] {
        display: none;
    }

    .checkmark {
        width: 20px;
        height: 20px;
        border: 2px solid #e1e5e9;
        border-radius: 4px;
        margin-right: 10px;
        position: relative;
        transition: all 0.3s ease;
    }

    .checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
        background: #4facfe;
        border-color: #4facfe;
    }

    .checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .login-btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 12px;
        color: white;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 10px;
    }

    .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
    }

    .login-btn:active {
        transform: translateY(0);
    }

    .login-btn i {
        margin-right: 8px;
    }

    .login-links {
        text-align: center;
        margin-top: 25px;
    }

    .forgot-link {
        color: #4facfe;
        text-decoration: none;
        font-size: 0.9rem;
        transition: color 0.3s ease;
    }

    .forgot-link:hover {
        color: #2196f3;
        text-decoration: underline;
    }

    .login-footer {
        background: #f8f9fa;
        padding: 20px 30px;
        text-align: center;
        border-top: 1px solid #e1e5e9;
    }

    .version-info {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 10px;
    }

    .status-online {
        color: #28a745;
    }

    .dev-mode-indicator {
        background: #fff3cd;
        color: #856404;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        display: inline-block;
    }

    .error-message {
        color: #dc3545;
        font-size: 0.8rem;
        margin-top: 5px;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @media (max-width: 480px) {
        .login-container {
            padding: 10px;
        }

        .login-header {
            padding: 30px 20px;
        }

        .login-body {
            padding: 30px 20px;
        }

        .app-title {
            font-size: 1.3rem;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
        }

        .logo-icon i {
            font-size: 2rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility
        const togglePassword = document.getElementById('togglePassword');
        if (togglePassword) {
            togglePassword.addEventListener('click', function() {
                const passwordField = document.getElementById('password');
                const icon = this.querySelector('i');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }

        // Form submission with loading state
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', function() {
                const btn = document.getElementById('loginBtn');
                if (btn) {
                    btn.disabled = true;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
                }
            });
        }

        // Auto-focus on username field
        const usernameField = document.getElementById('username');
        if (usernameField) {
            usernameField.focus();
        }

        // Check system status
        checkSystemStatus();
    });

    function checkSystemStatus() {
        fetch('<?= url('/api/system-status') ?>')
            .then(response => response.json())
            .then(data => {
                const statusElement = document.getElementById('systemStatus');
                if (statusElement) {
                    if (data.status === 'online') {
                        statusElement.innerHTML = '<i class="fas fa-circle text-success"></i> System Online';
                    } else {
                        statusElement.innerHTML = '<i class="fas fa-circle text-warning"></i> System Issues';
                    }
                }
            })
            .catch(() => {
                const statusElement = document.getElementById('systemStatus');
                if (statusElement) {
                    statusElement.innerHTML = '<i class="fas fa-circle text-danger"></i> System Offline';
                }
            });
    }
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
