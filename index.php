<?php
/**
 * Client Domain & Server Management System
 * Entry Point - Front Controller
 * 
 * <AUTHOR> Name
 * @version 1.0
 */

// Start session
session_start();

// Define constants
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('CACHE_PATH', ROOT_PATH . '/cache');
define('DATABASE_PATH', ROOT_PATH . '/database');

// Environment detection
define('IS_LOCAL', in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', '::1']) || 
                   strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0);

// Error reporting based on environment
if (IS_LOCAL) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    define('DEBUG_MODE', true);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    define('DEBUG_MODE', false);
}

// Autoloader
spl_autoload_register(function ($class) {
    $paths = [
        APP_PATH . '/controllers/',
        APP_PATH . '/models/',
        APP_PATH . '/middleware/',
        APP_PATH . '/core/',
        APP_PATH . '/helpers/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Include core files
require_once APP_PATH . '/core/Database.php';
require_once APP_PATH . '/core/Router.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Cache.php';
require_once APP_PATH . '/core/Auth.php';
require_once APP_PATH . '/helpers/Helper.php';

// Load configuration
$config = require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';

// Set timezone
date_default_timezone_set($config['app']['timezone']);

// Initialize database and run migrations
try {
    $db = Database::getInstance();

    // Run migrations
    $db->runMigrations();

} catch (Exception $e) {
    if (DEBUG_MODE) {
        die("Database Error: " . $e->getMessage());
    } else {
        die("System temporarily unavailable. Please try again later.");
    }
}

// Check remember me token
AuthController::checkRememberMe();

// Include routes
include ROOT_PATH . '/routes.php';
