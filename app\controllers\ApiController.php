<?php
/**
 * API Controller
 * Handles AJAX requests
 */

class ApiController extends Controller
{
    private $domainModel;
    private $clientModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->domainModel = new Domain();
        $this->clientModel = new Client();
    }
    
    /**
     * Get domains for a specific client
     */
    public function getClientDomains()
    {
        // Set JSON header
        header('Content-Type: application/json');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Content-Type');
        
        try {
            $clientId = $_GET['client_id'] ?? $_POST['client_id'] ?? null;
            
            if (!$clientId) {
                http_response_code(400);
                echo json_encode(['error' => 'Client ID is required']);
                exit;
            }
            
            // Validate client exists
            $client = $this->clientModel->find($clientId);
            if (!$client) {
                http_response_code(404);
                echo json_encode(['error' => 'Client not found']);
                exit;
            }
            
            // Get domains for this client
            $domains = $this->domainModel->where([
                'client_id' => $clientId,
                'status' => 'active'
            ], 'domain_name ASC');
            
            // Return domains
            echo json_encode($domains);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
        
        exit;
    }
    
    /**
     * Test endpoint
     */
    public function test()
    {
        header('Content-Type: application/json');
        echo json_encode(['status' => 'API is working', 'timestamp' => date('Y-m-d H:i:s')]);
        exit;
    }
}
