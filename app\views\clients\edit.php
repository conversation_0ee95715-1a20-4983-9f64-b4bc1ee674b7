<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-user-edit me-2 text-primary"></i>
                Edit Client - <?= e($client['name']) ?>
            </h1>
            <a href="<?= url('/clients/' . $client['id']) ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Client
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Client Information
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/clients/' . $client['id']) ?>" id="clientForm">
                    <?= csrf_field() ?>
                    <?= method_field('PUT') ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    Full Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?= has_error('name') ? 'is-invalid' : '' ?>" 
                                       id="name" 
                                       name="name" 
                                       value="<?= e(old('name', $client['name'])) ?>"
                                       placeholder="Enter client's full name"
                                       required>
                                <?php if (has_error('name')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('name')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" 
                                       class="form-control <?= has_error('company') ? 'is-invalid' : '' ?>" 
                                       id="company" 
                                       name="company" 
                                       value="<?= e(old('company', $client['company'])) ?>"
                                       placeholder="Company name (optional)">
                                <?php if (has_error('company')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('company')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" 
                                       class="form-control <?= has_error('email') ? 'is-invalid' : '' ?>" 
                                       id="email" 
                                       name="email" 
                                       value="<?= e(old('email', $client['email'])) ?>"
                                       placeholder="<EMAIL>">
                                <?php if (has_error('email')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('email')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" 
                                       class="form-control <?= has_error('phone') ? 'is-invalid' : '' ?>" 
                                       id="phone" 
                                       name="phone" 
                                       value="<?= e(old('phone', $client['phone'])) ?>"
                                       placeholder="+****************">
                                <?php if (has_error('phone')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('phone')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control <?= has_error('address') ? 'is-invalid' : '' ?>" 
                                  id="address" 
                                  name="address" 
                                  rows="3"
                                  placeholder="Enter full address"><?= e(old('address', $client['address'])) ?></textarea>
                        <?php if (has_error('address')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('address')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control <?= has_error('notes') ? 'is-invalid' : '' ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4"
                                  placeholder="Additional notes about the client"><?= e(old('notes', $client['notes'])) ?></textarea>
                        <?php if (has_error('notes')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('notes')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1"
                                   <?= old('is_active', $client['is_active']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_active">
                                Active Client
                            </label>
                            <div class="form-text">
                                Inactive clients will be hidden from most lists
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= url('/clients/' . $client['id']) ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Client
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-1"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> Editing Client</h6>
                    <ul class="mb-0 small">
                        <li>Only the client name is required</li>
                        <li>Email and phone help with communication</li>
                        <li>Company field is useful for business clients</li>
                        <li>Use notes for special requirements or preferences</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Important</h6>
                    <p class="mb-0 small">
                        Deactivating a client will hide them from most lists but 
                        will not affect their existing domains and servers.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-chart-line me-1"></i>
                    Client Summary
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-0"><?= count($client['domains'] ?? []) ?></h4>
                            <small class="text-muted">Domains</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-0"><?= count($client['servers'] ?? []) ?></h4>
                        <small class="text-muted">Servers</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h5 class="text-success mb-0"><?= format_currency($client['total_value'] ?? 0) ?></h5>
                    <small class="text-muted">Total Value</small>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <small class="text-muted">Member Since</small><br>
                    <strong><?= format_date($client['created_at'], 'M j, Y') ?></strong>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Form validation
    $('#clientForm').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const name = $('#name').val().trim();
        if (!name) {
            $('#name').addClass('is-invalid');
            isValid = false;
        } else {
            $('#name').removeClass('is-invalid');
        }
        
        // Validate email if provided
        const email = $('#email').val().trim();
        if (email && !isValidEmail(email)) {
            $('#email').addClass('is-invalid');
            isValid = false;
        } else {
            $('#email').removeClass('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            ClientManager.showAlert('Please correct the errors below.', 'error');
        }
    });
    
    // Real-time validation
    $('#email').on('blur', function() {
        const email = $(this).val().trim();
        if (email && !isValidEmail(email)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    $('#name').on('input', function() {
        if ($(this).val().trim()) {
            $(this).removeClass('is-invalid');
        }
    });
});

function isValidEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
