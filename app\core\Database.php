<?php
/**
 * Database Class - <PERSON>ton Pattern
 * Handles database connections and migrations
 */

class Database
{
    private static $instance = null;
    private $connection;
    private $config;
    
    private function __construct()
    {
        $this->config = include CONFIG_PATH . '/database.php';
        $this->connect();
    }
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect()
    {
        $config = $this->config['connections'][$this->config['default']];

        try {
            if ($config['driver'] === 'mysql') {
                // First connect without database to check if it exists
                $dsn = "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}";
                $this->connection = new PDO($dsn, $config['username'], $config['password'], $config['options']);

                // Check if database exists, if not create it
                if (!$this->databaseExists()) {
                    $this->createDatabase();
                }

                // Now connect to the specific database
                $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
                $this->connection = new PDO($dsn, $config['username'], $config['password'], $config['options']);

            } elseif ($config['driver'] === 'sqlite') {
                $dsn = "sqlite:{$config['database']}";
                $this->connection = new PDO($dsn, null, null, $config['options']);
            }
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function getConnection()
    {
        return $this->connection;
    }
    
    public function databaseExists()
    {
        $config = $this->config['connections'][$this->config['default']];

        if ($config['driver'] === 'mysql') {
            try {
                $stmt = $this->connection->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
                $stmt->execute([$config['database']]);
                return $stmt->rowCount() > 0;
            } catch (PDOException $e) {
                return false;
            }
        } elseif ($config['driver'] === 'sqlite') {
            return file_exists($config['database']);
        }

        return false;
    }
    
    public function createDatabase()
    {
        $config = $this->config['connections'][$this->config['default']];
        
        if ($config['driver'] === 'mysql') {
            $this->connection->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET {$config['charset']} COLLATE {$config['collation']}");
            $this->connection->exec("USE `{$config['database']}`");
        } elseif ($config['driver'] === 'sqlite') {
            // Create directory if it doesn't exist
            $dir = dirname($config['database']);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            // SQLite database is created automatically when first accessed
        }
    }
    
    public function runMigrations()
    {
        // Create migrations table if it doesn't exist
        $this->createMigrationsTable();
        
        // Get all migration files
        $migrationPath = $this->config['migration_path'];
        if (!is_dir($migrationPath)) {
            mkdir($migrationPath, 0755, true);
            $this->createInitialMigrations();
        }
        
        $files = glob($migrationPath . '/*.php');
        sort($files);
        
        foreach ($files as $file) {
            $filename = basename($file, '.php');
            
            // Check if migration has already been run
            $stmt = $this->connection->prepare("SELECT id FROM {$this->config['migrations_table']} WHERE migration = ?");
            $stmt->execute([$filename]);
            
            if ($stmt->rowCount() === 0) {
                // Run migration
                include $file;
                
                // Record migration
                $stmt = $this->connection->prepare("INSERT INTO {$this->config['migrations_table']} (migration, executed_at) VALUES (?, NOW())");
                $stmt->execute([$filename]);
            }
        }
    }
    
    private function createMigrationsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS {$this->config['migrations_table']} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration VARCHAR(255) NOT NULL,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $this->connection->exec($sql);
    }
    
    private function createInitialMigrations()
    {
        $migrations = [
            '001_create_users_table.php',
            '002_create_clients_table.php',
            '003_create_mediators_table.php',
            '004_create_domains_table.php',
            '005_create_servers_table.php',
            '006_create_notifications_table.php',
            '007_create_audit_logs_table.php'
        ];
        
        foreach ($migrations as $migration) {
            $this->createMigrationFile($migration);
        }
    }
    
    private function createMigrationFile($filename)
    {
        $path = $this->config['migration_path'] . '/' . $filename;
        
        if (file_exists($path)) {
            return;
        }
        
        $content = $this->getMigrationContent($filename);
        file_put_contents($path, $content);
    }
    
    private function getMigrationContent($filename)
    {
        switch ($filename) {
            case '001_create_users_table.php':
                return $this->getUsersTableMigration();
            case '002_create_clients_table.php':
                return $this->getClientsTableMigration();
            case '003_create_mediators_table.php':
                return $this->getMediatorsTableMigration();
            case '004_create_domains_table.php':
                return $this->getDomainsTableMigration();
            case '005_create_servers_table.php':
                return $this->getServersTableMigration();
            case '006_create_notifications_table.php':
                return $this->getNotificationsTableMigration();
            case '007_create_audit_logs_table.php':
                return $this->getAuditLogsTableMigration();
            default:
                return "<?php\n// Migration: $filename\n";
        }
    }
    
    private function getUsersTableMigration()
    {
        return '<?php
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM(\'admin\', \'staff\') DEFAULT \'staff\',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

$this->connection->exec($sql);

// Create default admin user
$hashedPassword = password_hash(\'admin123\', PASSWORD_DEFAULT);
$stmt = $this->connection->prepare("INSERT IGNORE INTO users (username, email, password, first_name, last_name, role) VALUES (?, ?, ?, ?, ?, ?)");
$stmt->execute([\'admin\', \'<EMAIL>\', $hashedPassword, \'System\', \'Administrator\', \'admin\']);
';
    }
    
    private function getClientsTableMigration()
    {
        return '<?php
$sql = "CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    company VARCHAR(100),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

$this->connection->exec($sql);
';
    }

    private function getMediatorsTableMigration()
    {
        return '<?php
$sql = "CREATE TABLE IF NOT EXISTS mediators (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    company VARCHAR(100),
    commission_rate DECIMAL(5,2) DEFAULT 0.00,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

$this->connection->exec($sql);
';
    }

    private function getDomainsTableMigration()
    {
        return '<?php
$sql = "CREATE TABLE IF NOT EXISTS domains (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    mediator_id INT,
    domain_name VARCHAR(255) NOT NULL,
    registrar VARCHAR(100),
    purchase_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    renewal_cost DECIMAL(10,2),
    auto_renewal BOOLEAN DEFAULT FALSE,
    nameservers TEXT,
    status ENUM(\'active\', \'expired\', \'suspended\', \'transferred\') DEFAULT \'active\',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (mediator_id) REFERENCES mediators(id) ON DELETE SET NULL
)";

$this->connection->exec($sql);
';
    }

    private function getServersTableMigration()
    {
        return '<?php
$sql = "CREATE TABLE IF NOT EXISTS servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    mediator_id INT,
    server_name VARCHAR(100) NOT NULL,
    provider VARCHAR(100),
    server_type ENUM(\'shared\', \'vps\', \'dedicated\', \'cloud\') DEFAULT \'shared\',
    ip_address VARCHAR(45),
    purchase_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    renewal_cost DECIMAL(10,2),
    auto_renewal BOOLEAN DEFAULT FALSE,
    specifications TEXT,
    status ENUM(\'active\', \'expired\', \'suspended\', \'terminated\') DEFAULT \'active\',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (mediator_id) REFERENCES mediators(id) ON DELETE SET NULL
)";

$this->connection->exec($sql);
';
    }

    private function getNotificationsTableMigration()
    {
        return '<?php
$sql = "CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type ENUM(\'domain_expiry\', \'server_expiry\', \'system\', \'info\') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    related_id INT,
    related_type ENUM(\'domain\', \'server\', \'client\'),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

$this->connection->exec($sql);
';
    }

    private function getAuditLogsTableMigration()
    {
        return '<?php
$sql = "CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

$this->connection->exec($sql);
';
    }
}
