<?php
/**
 * Cache Class
 * Handles caching operations for improved performance
 */

class Cache
{
    private $config;
    private $cachePath;
    
    public function __construct()
    {
        $this->config = include CONFIG_PATH . '/config.php';
        $this->cachePath = $this->config['cache']['path'];
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cachePath)) {
            mkdir($this->cachePath, 0755, true);
        }
    }
    
    /**
     * Get cached data
     */
    public function get($key, $default = null)
    {
        if (!$this->config['cache']['enabled']) {
            return $default;
        }
        
        $filename = $this->getFilename($key);
        
        if (!file_exists($filename)) {
            return $default;
        }
        
        $data = file_get_contents($filename);
        $data = unserialize($data);
        
        // Check if expired
        if ($data['expires'] && time() > $data['expires']) {
            $this->forget($key);
            return $default;
        }
        
        return $data['value'];
    }
    
    /**
     * Store data in cache
     */
    public function put($key, $value, $ttl = null)
    {
        if (!$this->config['cache']['enabled']) {
            return false;
        }
        
        $ttl = $ttl ?: $this->config['cache']['ttl'];
        $expires = $ttl ? time() + $ttl : null;
        
        $data = [
            'value' => $value,
            'expires' => $expires,
            'created' => time()
        ];
        
        $filename = $this->getFilename($key);
        return file_put_contents($filename, serialize($data)) !== false;
    }
    
    /**
     * Remember data (get or put)
     */
    public function remember($key, $callback, $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        $this->put($key, $value, $ttl);
        
        return $value;
    }
    
    /**
     * Check if key exists in cache
     */
    public function has($key)
    {
        return $this->get($key) !== null;
    }
    
    /**
     * Remove item from cache
     */
    public function forget($key)
    {
        $filename = $this->getFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     */
    public function flush()
    {
        $files = glob($this->cachePath . '/*.cache');
        
        foreach ($files as $file) {
            unlink($file);
        }
        
        return true;
    }
    
    /**
     * Clean expired cache entries
     */
    public function clean()
    {
        $files = glob($this->cachePath . '/*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            $data = unserialize($data);
            
            if ($data['expires'] && time() > $data['expires']) {
                unlink($file);
                $cleaned++;
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Get cache statistics
     */
    public function stats()
    {
        $files = glob($this->cachePath . '/*.cache');
        $totalSize = 0;
        $expired = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $data = file_get_contents($file);
            $data = unserialize($data);
            
            if ($data['expires'] && time() > $data['expires']) {
                $expired++;
            }
        }
        
        return [
            'total_items' => count($files),
            'expired_items' => $expired,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }
    
    /**
     * Get cache filename for key
     */
    private function getFilename($key)
    {
        $hash = md5($key);
        return $this->cachePath . '/' . $hash . '.cache';
    }
    
    /**
     * Format bytes to human readable
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
