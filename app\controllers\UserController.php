<?php
/**
 * User Controller
 */

class UserController extends Controller
{
    private $userModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        
        // Check admin permission
        if (!Auth::isAdmin()) {
            $this->setFlashMessage('Access denied. Admin privileges required.', 'error');
            $this->redirect('/dashboard');
        }
    }
    
    /**
     * Display users list
     */
    public function index()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $filter = $_GET['filter'] ?? '';
        
        $conditions = [];
        if ($filter === 'admin') {
            $conditions['role'] = 'admin';
        } elseif ($filter === 'staff') {
            $conditions['role'] = 'staff';
        } elseif ($filter === 'active') {
            $conditions['is_active'] = 1;
        } elseif ($filter === 'inactive') {
            $conditions['is_active'] = 0;
        }
        
        if ($search) {
            $sql = "SELECT * FROM users 
                    WHERE (username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)
                    ORDER BY first_name ASC";
            $searchTerm = "%{$search}%";
            $users = $this->userModel->query($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            $pagination = null;
        } else {
            $result = $this->userModel->paginate($page, 20, $conditions, 'first_name ASC');
            $users = $result['data'];
            $pagination = $result['pagination'];
        }
        
        // Hide passwords
        $users = $this->userModel->hideFields($users);
        
        echo $this->view('users/index', [
            'title' => 'User Management',
            'users' => $users,
            'pagination' => $pagination,
            'search' => $search,
            'filter' => $filter,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show create user form
     */
    public function create()
    {
        echo $this->view('users/create', [
            'title' => 'Add New User',
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Store new user
     */
    public function store()
    {
        $this->checkCsrfToken();
        
        $rules = [
            'username' => 'required|min:3|max:50|unique:users,username',
            'email' => 'required|email|max:100|unique:users,email',
            'password' => 'required|min:8|confirmed',
            'first_name' => 'required|max:50',
            'last_name' => 'required|max:50',
            'role' => 'required'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/users/create');
        }
        
        $data = [
            'username' => $_POST['username'],
            'email' => $_POST['email'],
            'password' => $_POST['password'],
            'first_name' => $_POST['first_name'],
            'last_name' => $_POST['last_name'],
            'role' => $_POST['role'],
            'is_active' => 1
        ];
        
        $userId = $this->userModel->createUser($data);
        
        if ($userId) {
            $this->setFlashMessage('User created successfully!', 'success');
            $this->redirect('/users/' . $userId);
        } else {
            $this->setFlashMessage('Failed to create user.', 'error');
            $this->redirect('/users/create');
        }
    }
    
    /**
     * Show user details
     */
    public function show($id)
    {
        $user = $this->userModel->find($id);
        
        if (!$user) {
            $this->setFlashMessage('User not found.', 'error');
            $this->redirect('/users');
        }
        
        // Hide password
        $user = $this->userModel->hideFields($user);
        
        echo $this->view('users/show', [
            'title' => 'User Details - ' . $user['first_name'] . ' ' . $user['last_name'],
            'user_data' => $user,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show edit user form
     */
    public function edit($id)
    {
        $userData = $this->userModel->find($id);
        
        if (!$userData) {
            $this->setFlashMessage('User not found.', 'error');
            $this->redirect('/users');
        }
        
        // Hide password
        $userData = $this->userModel->hideFields($userData);
        
        echo $this->view('users/edit', [
            'title' => 'Edit User - ' . $userData['first_name'] . ' ' . $userData['last_name'],
            'user_data' => $userData,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update user
     */
    public function update($id)
    {
        $this->checkCsrfToken();
        
        $userData = $this->userModel->find($id);
        if (!$userData) {
            $this->setFlashMessage('User not found.', 'error');
            $this->redirect('/users');
        }
        
        $rules = [
            'username' => 'required|min:3|max:50',
            'email' => 'required|email|max:100',
            'first_name' => 'required|max:50',
            'last_name' => 'required|max:50',
            'role' => 'required'
        ];
        
        // Add password validation if password is provided
        if (!empty($_POST['password'])) {
            $rules['password'] = 'min:8|confirmed';
        }
        
        // Check for unique username and email (excluding current user)
        $existingUser = $this->userModel->findBy('username', $_POST['username']);
        if ($existingUser && $existingUser['id'] != $id) {
            $this->setFlashMessage('Username already exists.', 'error');
            $this->redirect('/users/' . $id . '/edit');
        }
        
        $existingUser = $this->userModel->findBy('email', $_POST['email']);
        if ($existingUser && $existingUser['id'] != $id) {
            $this->setFlashMessage('Email already exists.', 'error');
            $this->redirect('/users/' . $id . '/edit');
        }
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/users/' . $id . '/edit');
        }
        
        $data = [
            'username' => $_POST['username'],
            'email' => $_POST['email'],
            'first_name' => $_POST['first_name'],
            'last_name' => $_POST['last_name'],
            'role' => $_POST['role'],
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        // Add password if provided
        if (!empty($_POST['password'])) {
            $data['password'] = $_POST['password'];
        }
        
        if ($this->userModel->updateUser($id, $data)) {
            $this->setFlashMessage('User updated successfully!', 'success');
            $this->redirect('/users/' . $id);
        } else {
            $this->setFlashMessage('Failed to update user.', 'error');
            $this->redirect('/users/' . $id . '/edit');
        }
    }
    
    /**
     * Delete user
     */
    public function delete($id)
    {
        $this->checkCsrfToken();
        
        $userData = $this->userModel->find($id);
        if (!$userData) {
            $this->setFlashMessage('User not found.', 'error');
            $this->redirect('/users');
        }
        
        // Prevent deleting current user
        if ($id == Auth::id()) {
            $this->setFlashMessage('Cannot delete your own account.', 'error');
            $this->redirect('/users/' . $id);
        }
        
        // Prevent deleting the last admin
        if ($userData['role'] === 'admin') {
            $adminCount = $this->userModel->count(['role' => 'admin', 'is_active' => 1]);
            if ($adminCount <= 1) {
                $this->setFlashMessage('Cannot delete the last admin user.', 'error');
                $this->redirect('/users/' . $id);
            }
        }
        
        if ($this->userModel->delete($id)) {
            $this->setFlashMessage('User deleted successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to delete user.', 'error');
        }
        
        $this->redirect('/users');
    }
    
    /**
     * Toggle user status
     */
    public function toggleStatus($id)
    {
        $this->checkCsrfToken();
        
        $userData = $this->userModel->find($id);
        if (!$userData) {
            $this->setFlashMessage('User not found.', 'error');
            $this->redirect('/users');
        }
        
        // Prevent deactivating current user
        if ($id == Auth::id()) {
            $this->setFlashMessage('Cannot deactivate your own account.', 'error');
            $this->redirect('/users/' . $id);
        }
        
        $newStatus = $userData['is_active'] ? 0 : 1;
        
        if ($this->userModel->update($id, ['is_active' => $newStatus])) {
            $status = $newStatus ? 'activated' : 'deactivated';
            $this->setFlashMessage("User {$status} successfully!", 'success');
        } else {
            $this->setFlashMessage('Failed to update user status.', 'error');
        }
        
        $this->redirect('/users/' . $id);
    }
    
    /**
     * Reset user password
     */
    public function resetPassword($id)
    {
        $this->checkCsrfToken();
        
        $userData = $this->userModel->find($id);
        if (!$userData) {
            $this->setFlashMessage('User not found.', 'error');
            $this->redirect('/users');
        }
        
        $newPassword = Auth::generatePassword(12);
        
        if ($this->userModel->updateUser($id, ['password' => $newPassword])) {
            $this->setFlashMessage("Password reset successfully! New password: {$newPassword}", 'success');
            
            // TODO: Send email with new password
            
        } else {
            $this->setFlashMessage('Failed to reset password.', 'error');
        }
        
        $this->redirect('/users/' . $id);
    }
}
