/* Custom CSS for Client Manager System */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar Layout */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    z-index: 1000;
    overflow-y: auto;
    transition: all 0.3s ease;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.15);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.sidebar-brand {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand .brand-link {
    color: white;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.sidebar-brand .brand-link:hover {
    color: #f8f9fa;
}

.sidebar-user {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-panel {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-image {
    flex-shrink: 0;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    display: block;
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-sidebar .nav-item {
    margin-bottom: 0.25rem;
}

.nav-sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0;
    border: none;
    background: none;
}

.nav-sidebar .nav-link:hover {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    color: white;
    transform: translateX(5px);
}

.nav-sidebar .nav-link.active {
    background: linear-gradient(90deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    box-shadow: inset 4px 0 0 #fff, 0 2px 8px rgba(0, 123, 255, 0.3);
    transform: translateX(5px);
}

.nav-sidebar .nav-icon {
    width: 20px;
    text-align: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.nav-sidebar .nav-text {
    flex: 1;
}

.nav-header {
    padding: 1rem 1rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 1rem;
}

.nav-header:first-child {
    margin-top: 0;
}

/* Notification Badge Animation */
.nav-sidebar .nav-link .badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Main Content Area */
.main-content {
    margin-left: 280px;
    padding: 2rem;
    min-height: calc(100vh - 60px);
    transition: margin-left 0.3s ease;
}

.main-footer {
    margin-left: 280px;
    background-color: #f8f9fa;
    padding: 1rem 2rem;
    border-top: 1px solid #e3e6f0;
    transition: margin-left 0.3s ease;
}

/* Mobile Header */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    padding: 0 1rem;
    z-index: 1001;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    padding: 0.5rem;
    margin-right: 1rem;
}

.sidebar-toggle:hover {
    color: rgba(255, 255, 255, 0.8);
}

.mobile-brand {
    font-weight: 600;
    font-size: 1.1rem;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding-top: 80px;
    }

    .main-footer {
        margin-left: 0;
    }

    .sidebar-overlay.show {
        display: block;
    }
}

@media (min-width: 992px) {
    .mobile-header {
        display: none;
    }
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
}

.card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #1e7e34);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(45deg, #1e7e34, #155724);
    transform: translateY(-1px);
}

/* Tables */
.table {
    background-color: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    background-color: #f8f9fc;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Forms */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.25rem;
}

.badge-success {
    background-color: var(--success-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: #212529;
}

.badge-info {
    background-color: var(--info-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
}

.badge-dark {
    background-color: var(--dark-color);
}

/* Avatar */
.avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
}

/* Status indicators */
.status-active {
    color: var(--success-color);
}

.status-expired {
    color: var(--danger-color);
}

.status-suspended {
    color: var(--warning-color);
}

.status-terminated {
    color: var(--dark-color);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Additional Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn {
        font-size: 0.875rem;
    }

    .main-content {
        padding: 1rem;
    }

    .sidebar {
        width: 260px;
    }

    .user-panel {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .nav-sidebar .nav-link {
        padding: 0.6rem 1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
    
    body.dark-mode {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .dark-mode .card {
        background-color: var(--card-bg);
        color: var(--text-color);
    }
    
    .dark-mode .table {
        background-color: var(--card-bg);
        color: var(--text-color);
    }
    
    .dark-mode .form-control {
        background-color: var(--card-bg);
        border-color: #495057;
        color: var(--text-color);
    }
}

/* Modern Profile Page Styles */
.profile-page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 0;
}

/* Profile Cover Section */
.profile-cover {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1.5rem 0 4rem;
    overflow: hidden;
}
.pro-head{
    margin: 0 auto;
    max-width: 90%;
    padding: 1rem 0;
}

.profile-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.profile-cover-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.1) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 60px 60px;
    background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
    opacity: 0.1;
}

.profile-header-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    color: white;
}

.profile-avatar-wrapper {
    position: relative;
}

.profile-avatar-large {
    width: 80px;
    height: 80px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.avatar-status {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.avatar-status.online {
    background: #28a745;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.profile-email {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0 0 0.75rem 0;
}

.profile-badges {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.role-badge, .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.role-badge.role-admin {
    background: rgba(220, 53, 69, 0.2);
    color: #fff;
}

.role-badge.role-staff {
    background: rgba(0, 123, 255, 0.2);
    color: #fff;
}

.status-badge.active {
    background: rgba(40, 167, 69, 0.2);
    color: #fff;
}

.profile-actions {
    display: flex;
    gap: 0.75rem;
}

/* Profile Content */
.profile-content {
    margin-top: -4rem;
    position: relative;
    z-index: 3;
    padding: 0 0 2rem 0;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
}

/* Modern Cards */
.modern-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-card .card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.modern-card .card-title i {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 6px;
    font-size: 0.75rem;
}

.modern-card .card-subtitle {
    color: #6c757d;
    font-size: 0.75rem;
    margin: 0.25rem 0 0 0;
}

.modern-card .card-body {
    padding: 1.5rem;
}

/* Modern Form Styles */
.modern-form {
    max-width: 100%;
}

.form-section {
    margin-bottom: 2rem;
}

.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f1f3f4;
}

.section-title {
    font-size: 1rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.25rem 0;
}

.section-subtitle {
    color: #6c757d;
    font-size: 0.8rem;
    margin: 0;
}

.modern-input-group {
    position: relative;
    margin-bottom: 1.25rem;
}

.modern-input {
    width: 100%;
    height: 2.8rem;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: #fff;
    color: #2c3e50;
}

.modern-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.modern-input.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 0.9rem;
    z-index: 2;
    transition: all 0.3s ease;
}

.modern-input:focus + .input-icon {
    color: var(--primary-color);
}

.modern-label {
    position: absolute;
    left: 2.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 0.9rem;
    pointer-events: none;
    transition: all 0.3s ease;
    background: white;
    padding: 0 0.25rem;
}

.modern-input:focus ~ .modern-label,
.modern-input:not(:placeholder-shown) ~ .modern-label {
    top: 0;
    left: 2.25rem;
    font-size: 0.7rem;
    color: var(--primary-color);
    font-weight: 600;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.input-help {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6c757d;
    font-size: 0.7rem;
    margin-top: 0.25rem;
}

.security-notice {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #1976d2;
    font-weight: 500;
    font-size: 0.85rem;
}

.security-notice i {
    font-size: 1rem;
    color: #2196f3;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 2px solid #f1f3f4;
    margin-top: 1.5rem;
}

.btn-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 120px;
    justify-content: center;
}

.btn-modern.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
}

.btn-modern.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.btn-modern.btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-modern.btn-secondary:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
}

/* Stats Grid */
.stats-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateX(3px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.125rem;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Security Tips */
.security-tips {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 10px;
    border-left: 3px solid #ffc107;
    transition: all 0.3s ease;
}

.tip-item:hover {
    transform: translateX(3px);
    box-shadow: 0 3px 10px rgba(255, 193, 7, 0.2);
}

.tip-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.tip-content h6 {
    font-size: 0.875rem;
    font-weight: 700;
    color: #856404;
    margin: 0 0 0.25rem 0;
}

.tip-content p {
    font-size: 0.75rem;
    color: #6c5700;
    margin: 0;
    line-height: 1.4;
}

/* Responsive Design for Profile */
@media (max-width: 768px) {
    .profile-header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .profile-name {
        font-size: 1.25rem;
    }

    .profile-avatar-large {
        width: 70px;
        height: 70px;
        font-size: 1.5rem;
    }

    .modern-card .card-body {
        padding: 1rem;
    }

    .modern-card .card-header {
        padding: 0.75rem 1rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn-modern {
        width: 100%;
        min-width: auto;
    }

    .profile-cover {
        padding: 1rem 0 3rem;
    }

    .profile-content {
        margin-top: -1.5rem;
        max-width: 95%;
    }

    .modern-input {
        height: 2.5rem;
        padding: 0.5rem 0.5rem 0.5rem 2.25rem;
    }

    .input-icon {
        left: 0.5rem;
        font-size: 0.8rem;
    }

    .modern-label {
        left: 2.25rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 1200px) {
    .profile-content {
        max-width: 90%;
    }
}

@media (min-width: 1400px) {
    .profile-content {
        max-width: 75%;
    }
}

/* Utility classes */
.text-xs {
    font-size: 0.75rem;
}

.text-sm {
    font-size: 0.875rem;
}

.font-weight-bold {
    font-weight: 700;
}

.border-left-primary {
    border-left: 0.25rem solid var(--primary-color);
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color);
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color);
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color);
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color);
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Print styles */
@media print {
    .sidebar,
    .mobile-header,
    .sidebar-overlay,
    .btn,
    .alert,
    .card-header {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .main-footer {
        margin-left: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}
