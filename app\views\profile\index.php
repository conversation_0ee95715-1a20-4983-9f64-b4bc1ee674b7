<?php
ob_start();
?>

<!-- Modern Profile Page -->
<div class="profile-page">
    <!-- Profile Cover & Header -->
    <div class="profile-cover">
        <div class="profile-cover-bg"></div>
        <div class="container-fluid">
            <div class="row">
                <div class="pro-head">
                    <div class="profile-header-content">
                        <div class="profile-avatar-wrapper">
                            <div class="profile-avatar-large">
                                <?= avatar($user, 140) ?>
                                <div class="avatar-status online"></div>
                            </div>
                        </div>
                        <div class="profile-info">
                            <h1 class="profile-name"><?= e($user['first_name'] . ' ' . $user['last_name']) ?></h1>
                            <p class="profile-email"><?= e($user['email']) ?></p>
                            <div class="profile-badges">
                                <span class="role-badge role-<?= $user['role'] ?>">
                                    <i class="fas fa-<?= $user['role'] === 'admin' ? 'crown' : 'user' ?>"></i>
                                    <?= ucfirst($user['role']) ?>
                                </span>
                                <span class="status-badge active">
                                    <i class="fas fa-circle"></i>
                                    Active
                                </span>
                            </div>
                        </div>
                        <div class="profile-actions">
                            <a href="<?= url('/dashboard') ?>" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left"></i>
                                Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
        <div class="container-fluid">
            <div class="row g-4">
                <!-- Main Profile Form -->
                <div class="col-xl-8 col-lg-7">
                    <div class="profile-card modern-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-user-edit"></i>
                                <span>Personal Information</span>
                            </div>
                            <div class="card-subtitle">Update your profile details</div>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="<?= url('/profile') ?>" id="profileForm" class="modern-form">
                                <?= csrf_field() ?>

                                <!-- Basic Information Section -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <h6 class="section-title">Basic Information</h6>
                                        <p class="section-subtitle">Your personal details</p>
                                    </div>

                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="modern-input-group">
                                                <div class="input-icon">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <input type="text"
                                                       class="modern-input <?= has_error('first_name') ? 'error' : '' ?>"
                                                       id="first_name"
                                                       name="first_name"
                                                       value="<?= e(old('first_name', $user['first_name'])) ?>"
                                                       placeholder="First Name"
                                                       required>
                                                <label class="modern-label">First Name *</label>
                                                <?php if (has_error('first_name')): ?>
                                                    <div class="error-message">
                                                        <i class="fas fa-exclamation-circle"></i>
                                                        <?= e(error('first_name')) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="modern-input-group">
                                                <div class="input-icon">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <input type="text"
                                                       class="modern-input <?= has_error('last_name') ? 'error' : '' ?>"
                                                       id="last_name"
                                                       name="last_name"
                                                       value="<?= e(old('last_name', $user['last_name'])) ?>"
                                                       placeholder="Last Name"
                                                       required>
                                                <label class="modern-label">Last Name *</label>
                                                <?php if (has_error('last_name')): ?>
                                                    <div class="error-message">
                                                        <i class="fas fa-exclamation-circle"></i>
                                                        <?= e(error('last_name')) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="col-12">
                                            <div class="modern-input-group">
                                                <div class="input-icon">
                                                    <i class="fas fa-envelope"></i>
                                                </div>
                                                <input type="email"
                                                       class="modern-input <?= has_error('email') ? 'error' : '' ?>"
                                                       id="email"
                                                       name="email"
                                                       value="<?= e(old('email', $user['email'])) ?>"
                                                       placeholder="Email Address"
                                                       required>
                                                <label class="modern-label">Email Address *</label>
                                                <?php if (has_error('email')): ?>
                                                    <div class="error-message">
                                                        <i class="fas fa-exclamation-circle"></i>
                                                        <?= e(error('email')) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                    
                                <!-- Security Section -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <h6 class="section-title">Security Settings</h6>
                                        <p class="section-subtitle">Update your password (optional)</p>
                                    </div>

                                    <div class="security-notice">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>Leave password fields blank to keep your current password</span>
                                    </div>

                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="modern-input-group">
                                                <div class="input-icon">
                                                    <i class="fas fa-lock"></i>
                                                </div>
                                                <input type="password"
                                                       class="modern-input <?= has_error('password') ? 'error' : '' ?>"
                                                       id="password"
                                                       name="password"
                                                       placeholder="New Password">
                                                <label class="modern-label">New Password</label>
                                                <?php if (has_error('password')): ?>
                                                    <div class="error-message">
                                                        <i class="fas fa-exclamation-circle"></i>
                                                        <?= e(error('password')) ?>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="input-help">
                                                    <i class="fas fa-info-circle"></i>
                                                    Minimum 8 characters required
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="modern-input-group">
                                                <div class="input-icon">
                                                    <i class="fas fa-check-circle"></i>
                                                </div>
                                                <input type="password"
                                                       class="modern-input"
                                                       id="password_confirmation"
                                                       name="password_confirmation"
                                                       placeholder="Confirm Password">
                                                <label class="modern-label">Confirm Password</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                    
                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <button type="button" class="btn-modern btn-secondary" onclick="window.location.href='<?= url('/dashboard') ?>'">
                                        <i class="fas fa-arrow-left"></i>
                                        <span>Cancel</span>
                                    </button>
                                    <button type="submit" class="btn-modern btn-primary">
                                        <i class="fas fa-save"></i>
                                        <span>Update Profile</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
    
                <!-- Sidebar -->
                <div class="col-xl-4 col-lg-5">
                    <!-- Account Stats -->
                    <div class="profile-card modern-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-line"></i>
                                <span>Account Statistics</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value"><?= format_date($user['created_at'], 'M Y') ?></div>
                                        <div class="stat-label">Member Since</div>
                                    </div>
                                </div>

                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value">
                                            <?php if ($user['last_login']): ?>
                                                <?= format_date($user['last_login'], 'M j') ?>
                                            <?php else: ?>
                                                Never
                                            <?php endif; ?>
                                        </div>
                                        <div class="stat-label">Last Login</div>
                                    </div>
                                </div>

                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-shield-check"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value">Active</div>
                                        <div class="stat-label">Account Status</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
        
                    <!-- Security Tips -->
                    <div class="profile-card modern-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-shield-alt"></i>
                                <span>Security Guidelines</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="security-tips">
                                <div class="tip-item">
                                    <div class="tip-icon">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h6>Strong Password</h6>
                                        <p>Use at least 8 characters with mixed case, numbers, and symbols</p>
                                    </div>
                                </div>

                                <div class="tip-item">
                                    <div class="tip-icon">
                                        <i class="fas fa-sync-alt"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h6>Regular Updates</h6>
                                        <p>Change your password regularly and keep your account secure</p>
                                    </div>
                                </div>

                                <div class="tip-item">
                                    <div class="tip-icon">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h6>Account Safety</h6>
                                        <p>Never share credentials and always log out on shared devices</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            let isValid = true;

            // Check required fields
            const firstName = document.getElementById('first_name').value.trim();
            const lastName = document.getElementById('last_name').value.trim();
            const email = document.getElementById('email').value.trim();

            // Clear previous errors
            document.querySelectorAll('.modern-input').forEach(input => {
                input.classList.remove('error');
            });

            if (!firstName) {
                document.getElementById('first_name').classList.add('error');
                isValid = false;
            }

            if (!lastName) {
                document.getElementById('last_name').classList.add('error');
                isValid = false;
            }

            if (!email || !isValidEmail(email)) {
                document.getElementById('email').classList.add('error');
                isValid = false;
            }

            // Check password confirmation if password is provided
            const password = document.getElementById('password').value;
            const passwordConfirmation = document.getElementById('password_confirmation').value;

            if (password && password !== passwordConfirmation) {
                document.getElementById('password_confirmation').classList.add('error');
                isValid = false;
            }

            if (password && password.length < 8) {
                document.getElementById('password').classList.add('error');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                if (typeof ClientManager !== 'undefined') {
                    ClientManager.showAlert('Please correct the errors below.', 'error');
                } else {
                    alert('Please correct the errors below.');
                }
            }
        });
    }

    // Real-time validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const email = this.value.trim();
            if (!email || !isValidEmail(email)) {
                this.classList.add('error');
            } else {
                this.classList.remove('error');
            }
        });
    }

    const passwordConfirmationField = document.getElementById('password_confirmation');
    if (passwordConfirmationField) {
        passwordConfirmationField.addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmation = this.value;

            if (password && password !== confirmation) {
                this.classList.add('error');
            } else {
                this.classList.remove('error');
            }
        });
    }

    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            const password = this.value;
            if (password && password.length < 8) {
                this.classList.add('error');
            } else {
                this.classList.remove('error');
            }

            // Also check confirmation
            const confirmationField = document.getElementById('password_confirmation');
            if (confirmationField) {
                const confirmation = confirmationField.value;
                if (confirmation && password !== confirmation) {
                    confirmationField.classList.add('error');
                } else {
                    confirmationField.classList.remove('error');
                }
            }
        });
    }
});

function isValidEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}
</script>
