<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-server me-2 text-primary"></i>
                <?= e($server['server_name']) ?>
            </h1>
            <div>
                <a href="<?= url('/servers/' . $server['id'] . '/edit') ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i>
                    Edit Server
                </a>
                <a href="<?= url('/servers') ?>" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Servers
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Server Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Server Name</label>
                            <p class="form-control-plaintext">
                                <strong><?= e($server['server_name']) ?></strong>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Client</label>
                            <p class="form-control-plaintext">
                                <a href="<?= url('/clients/' . $server['client_id']) ?>" class="text-decoration-none">
                                    <?= e($server['client_name']) ?>
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Server Type</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6"><?= ucfirst($server['server_type']) ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Provider</label>
                            <p class="form-control-plaintext"><?= e($server['provider']) ?: '<span class="text-muted">Not specified</span>' ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">IP Address</label>
                            <p class="form-control-plaintext">
                                <?php if ($server['ip_address']): ?>
                                    <code><?= e($server['ip_address']) ?></code>
                                    <a href="http://<?= e($server['ip_address']) ?>" target="_blank" class="ms-2">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Mediator</label>
                            <p class="form-control-plaintext">
                                <?php if ($server['mediator_name']): ?>
                                    <a href="<?= url('/mediators/' . $server['mediator_id']) ?>" class="text-decoration-none">
                                        <?= e($server['mediator_name']) ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">No mediator</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Purchase Date</label>
                            <p class="form-control-plaintext"><?= format_date($server['purchase_date'], 'M j, Y') ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Expiry Date</label>
                            <p class="form-control-plaintext">
                                <?php 
                                $daysUntil = days_until($server['expiry_date']);
                                $expiryClass = '';
                                if ($daysUntil < 0) {
                                    $expiryClass = 'text-danger';
                                } elseif ($daysUntil <= 7) {
                                    $expiryClass = 'text-danger';
                                } elseif ($daysUntil <= 30) {
                                    $expiryClass = 'text-warning';
                                } else {
                                    $expiryClass = 'text-success';
                                }
                                ?>
                                <span class="<?= $expiryClass ?>">
                                    <strong><?= format_date($server['expiry_date'], 'M j, Y') ?></strong>
                                </span>
                                <br>
                                <small class="<?= $expiryClass ?>">
                                    <?php if ($daysUntil < 0): ?>
                                        Expired <?= abs($daysUntil) ?> days ago
                                    <?php else: ?>
                                        <?= $daysUntil ?> days remaining
                                    <?php endif; ?>
                                </small>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Current Cost</label>
                            <p class="form-control-plaintext">
                                <?php if ($server['current_cost'] > 0): ?>
                                    <strong class="text-success"><?= format_currency($server['current_cost']) ?></strong>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Auto Renewal</label>
                            <p class="form-control-plaintext">
                                <?php if ($server['auto_renewal']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        Enabled
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        Disabled
                                    </span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Status</label>
                            <p class="form-control-plaintext">
                                <?= status_badge($server['status']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <?php if ($server['specifications']): ?>
                <div class="mb-3">
                    <label class="form-label text-muted">Server Specifications</label>
                    <div class="form-control-plaintext">
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(e($server['specifications'])) ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($server['notes']): ?>
                <div class="mb-3">
                    <label class="form-label text-muted">Notes</label>
                    <p class="form-control-plaintext"><?= nl2br(e($server['notes'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Expiry Alert
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <?php 
                    $daysUntil = days_until($server['expiry_date']);
                    if ($daysUntil < 0): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                            <h6>Server Expired!</h6>
                            <p class="mb-0">This server expired <?= abs($daysUntil) ?> days ago.</p>
                        </div>
                    <?php elseif ($daysUntil <= 7): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <h6>Urgent Renewal Required!</h6>
                            <p class="mb-0">This server expires in <?= $daysUntil ?> days.</p>
                        </div>
                    <?php elseif ($daysUntil <= 30): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h6>Renewal Due Soon</h6>
                            <p class="mb-0">This server expires in <?= $daysUntil ?> days.</p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h6>Server Active</h6>
                            <p class="mb-0">This server expires in <?= $daysUntil ?> days.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-tools me-1"></i>
                    Quick Tools
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if ($server['ip_address']): ?>
                        <a href="http://<?= e($server['ip_address']) ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i>
                            Visit IP Address
                        </a>
                        
                        <a href="https://www.whatsmyip.org/ping/?host=<?= e($server['ip_address']) ?>" target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-satellite-dish me-1"></i>
                            Ping Server
                        </a>
                    <?php endif; ?>
                    
                    <a href="https://www.whatsmyip.org/port-scanner/?host=<?= e($server['ip_address'] ?? $server['server_name']) ?>" target="_blank" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-search me-1"></i>
                        Port Scanner
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-secondary">
                    <i class="fas fa-cog me-1"></i>
                    Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= url('/servers/' . $server['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>
                        Edit Server
                    </a>
                    
                    <form method="POST" action="<?= url('/servers/' . $server['id'] . '/renew') ?>" style="display: inline;">
                        <?= csrf_field() ?>
                        <button type="submit" class="btn btn-outline-success btn-sm w-100"
                                onclick="return confirm('Mark this server as renewed?')">
                            <i class="fas fa-sync-alt me-1"></i>
                            Mark as Renewed
                        </button>
                    </form>

                    <?php
                    $whatsappUrl = Helper::generateWhatsAppRenewalUrl(
                        'server',
                        $server['server_name'],
                        $server['expiry_date'],
                        $server['client_phone'],
                        $server['mediator_phone']
                    );
                    ?>
                    <?php if ($whatsappUrl): ?>
                    <a href="<?= $whatsappUrl ?>" target="_blank" class="btn btn-outline-success btn-sm">
                        <i class="fab fa-whatsapp me-1"></i>
                        Send Renewal Reminder
                        <?php if (!empty($server['mediator_phone'])): ?>
                            <small class="d-block text-muted">to Mediator</small>
                        <?php else: ?>
                            <small class="d-block text-muted">to Client</small>
                        <?php endif; ?>
                    </a>
                    <?php else: ?>
                    <button type="button" class="btn btn-outline-secondary btn-sm" disabled title="No WhatsApp number available">
                        <i class="fab fa-whatsapp me-1"></i>
                        Send Renewal Reminder
                        <small class="d-block text-muted">No phone number</small>
                    </button>
                    <?php endif; ?>

                    <button type="button" class="btn btn-outline-danger btn-sm"
                            onclick="deleteServer(<?= $server['id'] ?>, '<?= e($server['server_name']) ?>')">
                        <i class="fas fa-trash me-1"></i>
                        Delete Server
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete server <strong id="serverName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <?= method_field('DELETE') ?>
                    <button type="submit" class="btn btn-danger">Delete Server</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteServer(id, name) {
    document.getElementById('serverName').textContent = name;
    document.getElementById('deleteForm').action = '<?= url('/servers/') ?>' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
