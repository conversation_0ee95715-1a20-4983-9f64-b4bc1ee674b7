<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-user me-2 text-primary"></i>
                <?= e($client['name']) ?>
            </h1>
            <div>
                <a href="<?= url('/clients/' . $client['id'] . '/edit') ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i>
                    Edit Client
                </a>
                <a href="<?= url('/clients') ?>" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Clients
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Client Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Full Name</label>
                            <p class="form-control-plaintext"><?= e($client['name']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Company</label>
                            <p class="form-control-plaintext"><?= e($client['company']) ?: '<span class="text-muted">Not specified</span>' ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="form-control-plaintext">
                                <?php if ($client['email']): ?>
                                    <a href="mailto:<?= e($client['email']) ?>"><?= e($client['email']) ?></a>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Phone</label>
                            <p class="form-control-plaintext">
                                <?php if ($client['phone']): ?>
                                    <a href="tel:<?= e($client['phone']) ?>"><?= e($client['phone']) ?></a>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <?php if ($client['address']): ?>
                <div class="mb-3">
                    <label class="form-label text-muted">Address</label>
                    <p class="form-control-plaintext"><?= nl2br(e($client['address'])) ?></p>
                </div>
                <?php endif; ?>
                
                <?php if ($client['notes']): ?>
                <div class="mb-3">
                    <label class="form-label text-muted">Notes</label>
                    <p class="form-control-plaintext"><?= nl2br(e($client['notes'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Domains Section -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-globe me-1"></i>
                    Domains (<?= count($client['domains'] ?? []) ?>)
                </h6>
                <a href="<?= url('/domains/create?client_id=' . $client['id']) ?>" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    Add Domain
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($client['domains'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-globe fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No domains found for this client.</p>
                        <a href="<?= url('/domains/create?client_id=' . $client['id']) ?>" class="btn btn-outline-success">
                            <i class="fas fa-plus me-1"></i>
                            Add First Domain
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Domain Name</th>
                                    <th>Expiry Date</th>
                                    <th>Status</th>
                                    <th>Current Cost</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($client['domains'] as $domain): ?>
                                <tr>
                                    <td><?= e($domain['domain_name']) ?></td>
                                    <td>
                                        <?php 
                                        $daysUntil = days_until($domain['expiry_date']);
                                        $class = $daysUntil < 0 ? 'text-danger' : ($daysUntil <= 30 ? 'text-warning' : 'text-success');
                                        ?>
                                        <span class="<?= $class ?>">
                                            <?= format_date($domain['expiry_date'], 'M j, Y') ?>
                                        </span>
                                    </td>
                                    <td><?= status_badge($domain['status']) ?></td>
                                    <td><?= format_currency($domain['current_cost']) ?></td>
                                    <td>
                                        <a href="<?= url('/domains/' . $domain['id']) ?>" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Servers Section -->
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-server me-1"></i>
                    Servers (<?= count($client['servers'] ?? []) ?>)
                </h6>
                <a href="<?= url('/servers/create?client_id=' . $client['id']) ?>" class="btn btn-info btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    Add Server
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($client['servers'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-server fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No servers found for this client.</p>
                        <a href="<?= url('/servers/create?client_id=' . $client['id']) ?>" class="btn btn-outline-info">
                            <i class="fas fa-plus me-1"></i>
                            Add First Server
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Server Name</th>
                                    <th>Type</th>
                                    <th>Expiry Date</th>
                                    <th>Status</th>
                                    <th>Current Cost</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($client['servers'] as $server): ?>
                                <tr>
                                    <td><?= e($server['server_name']) ?></td>
                                    <td><span class="badge bg-secondary"><?= ucfirst($server['server_type']) ?></span></td>
                                    <td>
                                        <?php 
                                        $daysUntil = days_until($server['expiry_date']);
                                        $class = $daysUntil < 0 ? 'text-danger' : ($daysUntil <= 30 ? 'text-warning' : 'text-success');
                                        ?>
                                        <span class="<?= $class ?>">
                                            <?= format_date($server['expiry_date'], 'M j, Y') ?>
                                        </span>
                                    </td>
                                    <td><?= status_badge($server['status']) ?></td>
                                    <td><?= format_currency($server['current_cost']) ?></td>
                                    <td>
                                        <a href="<?= url('/servers/' . $server['id']) ?>" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-chart-line me-1"></i>
                    Summary
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <h4 class="text-success"><?= format_currency($client['total_value'] ?? 0) ?></h4>
                        <small class="text-muted">Total Value</small>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-primary"><?= count($client['domains'] ?? []) ?></h6>
                                <small class="text-muted">Domains</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-info"><?= count($client['servers'] ?? []) ?></h6>
                            <small class="text-muted">Servers</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-cog me-1"></i>
                    Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= url('/clients/' . $client['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>
                        Edit Client
                    </a>
                    
                    <a href="<?= url('/domains/create?client_id=' . $client['id']) ?>" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-globe me-1"></i>
                        Add Domain
                    </a>
                    
                    <a href="<?= url('/servers/create?client_id=' . $client['id']) ?>" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-server me-1"></i>
                        Add Server
                    </a>
                    
                    <button type="button" class="btn btn-outline-danger btn-sm" 
                            onclick="deleteClient(<?= $client['id'] ?>, '<?= e($client['name']) ?>')">
                        <i class="fas fa-trash me-1"></i>
                        Delete Client
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete client <strong id="clientName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <?= method_field('DELETE') ?>
                    <button type="submit" class="btn btn-danger">Delete Client</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteClient(id, name) {
    document.getElementById('clientName').textContent = name;
    document.getElementById('deleteForm').action = '<?= url('/clients/') ?>' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
