<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-cog me-2 text-primary"></i>
                System Settings
            </h1>
            <a href="<?= url('/dashboard') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-envelope me-1"></i>
                    Email Configuration
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/settings/email') ?>" id="emailForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_host" class="form-label">SMTP Host</label>
                                <input type="text" 
                                       class="form-control <?= has_error('smtp_host') ? 'is-invalid' : '' ?>" 
                                       id="smtp_host" 
                                       name="smtp_host" 
                                       value="<?= e(old('smtp_host', $settings['smtp_host'] ?? '')) ?>"
                                       placeholder="smtp.gmail.com">
                                <?php if (has_error('smtp_host')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('smtp_host')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                <input type="number" 
                                       class="form-control <?= has_error('smtp_port') ? 'is-invalid' : '' ?>" 
                                       id="smtp_port" 
                                       name="smtp_port" 
                                       value="<?= e(old('smtp_port', $settings['smtp_port'] ?? '587')) ?>"
                                       placeholder="587">
                                <?php if (has_error('smtp_port')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('smtp_port')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_username" class="form-label">SMTP Username</label>
                                <input type="text" 
                                       class="form-control <?= has_error('smtp_username') ? 'is-invalid' : '' ?>" 
                                       id="smtp_username" 
                                       name="smtp_username" 
                                       value="<?= e(old('smtp_username', $settings['smtp_username'] ?? '')) ?>"
                                       placeholder="<EMAIL>">
                                <?php if (has_error('smtp_username')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('smtp_username')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_password" class="form-label">SMTP Password</label>
                                <input type="password" 
                                       class="form-control <?= has_error('smtp_password') ? 'is-invalid' : '' ?>" 
                                       id="smtp_password" 
                                       name="smtp_password" 
                                       value="<?= e(old('smtp_password', $settings['smtp_password'] ?? '')) ?>"
                                       placeholder="Your app password">
                                <?php if (has_error('smtp_password')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('smtp_password')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="from_email" class="form-label">From Email</label>
                                <input type="email" 
                                       class="form-control <?= has_error('from_email') ? 'is-invalid' : '' ?>" 
                                       id="from_email" 
                                       name="from_email" 
                                       value="<?= e(old('from_email', $settings['from_email'] ?? '')) ?>"
                                       placeholder="<EMAIL>">
                                <?php if (has_error('from_email')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('from_email')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="from_name" class="form-label">From Name</label>
                                <input type="text" 
                                       class="form-control <?= has_error('from_name') ? 'is-invalid' : '' ?>" 
                                       id="from_name" 
                                       name="from_name" 
                                       value="<?= e(old('from_name', $settings['from_name'] ?? '')) ?>"
                                       placeholder="Your Company Name">
                                <?php if (has_error('from_name')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('from_name')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="smtp_encryption" 
                                   name="smtp_encryption" 
                                   value="tls"
                                   <?= old('smtp_encryption', $settings['smtp_encryption'] ?? 'tls') == 'tls' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="smtp_encryption">
                                Use TLS Encryption
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-info" onclick="testEmail()">
                            <i class="fas fa-paper-plane me-1"></i>
                            Test Email
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Email Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-bell me-1"></i>
                    Notification Settings
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/settings/notifications') ?>" id="notificationForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="domain_alert_days" class="form-label">Domain Alert Days</label>
                                <select class="form-select" id="domain_alert_days" name="domain_alert_days">
                                    <option value="30,15,7,1" <?= ($settings['domain_alert_days'] ?? '30,15,7,1') == '30,15,7,1' ? 'selected' : '' ?>>30, 15, 7, 1 days</option>
                                    <option value="30,7,1" <?= ($settings['domain_alert_days'] ?? '') == '30,7,1' ? 'selected' : '' ?>>30, 7, 1 days</option>
                                    <option value="15,7,1" <?= ($settings['domain_alert_days'] ?? '') == '15,7,1' ? 'selected' : '' ?>>15, 7, 1 days</option>
                                    <option value="7,1" <?= ($settings['domain_alert_days'] ?? '') == '7,1' ? 'selected' : '' ?>>7, 1 days</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="server_alert_days" class="form-label">Server Alert Days</label>
                                <select class="form-select" id="server_alert_days" name="server_alert_days">
                                    <option value="30,15,7,1" <?= ($settings['server_alert_days'] ?? '30,15,7,1') == '30,15,7,1' ? 'selected' : '' ?>>30, 15, 7, 1 days</option>
                                    <option value="30,7,1" <?= ($settings['server_alert_days'] ?? '') == '30,7,1' ? 'selected' : '' ?>>30, 7, 1 days</option>
                                    <option value="15,7,1" <?= ($settings['server_alert_days'] ?? '') == '15,7,1' ? 'selected' : '' ?>>15, 7, 1 days</option>
                                    <option value="7,1" <?= ($settings['server_alert_days'] ?? '') == '7,1' ? 'selected' : '' ?>>7, 1 days</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="enable_email_alerts" 
                                   name="enable_email_alerts" 
                                   value="1"
                                   <?= old('enable_email_alerts', $settings['enable_email_alerts'] ?? 1) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="enable_email_alerts">
                                Enable Email Alerts
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="alert_email" class="form-label">Alert Email Address</label>
                        <input type="email" 
                               class="form-control" 
                               id="alert_email" 
                               name="alert_email" 
                               value="<?= e(old('alert_email', $settings['alert_email'] ?? '')) ?>"
                               placeholder="<EMAIL>">
                        <div class="form-text">Email address to receive expiry alerts</div>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>
                        Save Notification Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-1"></i>
                    Email Setup Guide
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-envelope me-1"></i> Gmail Setup</h6>
                    <ul class="mb-0 small">
                        <li>Host: smtp.gmail.com</li>
                        <li>Port: 587</li>
                        <li>Use app password, not regular password</li>
                        <li>Enable 2-factor authentication first</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Security</h6>
                    <p class="mb-0 small">
                        Never use your regular email password. Always use 
                        app-specific passwords for better security.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-database me-1"></i>
                    System Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <small class="text-muted d-block">PHP Version</small>
                        <strong><?= PHP_VERSION ?></strong>
                    </div>
                    <div class="col-12 mb-3">
                        <small class="text-muted d-block">Database</small>
                        <strong>MySQL</strong>
                    </div>
                    <div class="col-12 mb-3">
                        <small class="text-muted d-block">Cache Status</small>
                        <span class="badge bg-success">Enabled</span>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="clearCache()">
                        <i class="fas fa-broom me-1"></i>
                        Clear Cache
                    </button>
                    
                    <a href="<?= url('/settings/backup') ?>" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-download me-1"></i>
                        Download Backup
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Danger Zone
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Reset System</h6>
                    <p class="mb-2 small">
                        This will delete all data and reset the system to default state.
                    </p>
                    <button type="button" class="btn btn-danger btn-sm" onclick="resetSystem()">
                        <i class="fas fa-trash me-1"></i>
                        Reset System
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testEmail() {
    const formData = new FormData(document.getElementById('emailForm'));
    
    fetch('<?= url('/settings/test-email') ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            ClientManager.showAlert('Test email sent successfully!', 'success');
        } else {
            ClientManager.showAlert('Failed to send test email: ' + data.message, 'error');
        }
    })
    .catch(error => {
        ClientManager.showAlert('Error sending test email.', 'error');
    });
}

function clearCache() {
    if (confirm('Are you sure you want to clear the cache?')) {
        fetch('<?= url('/settings/clear-cache') ?>', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': '<?= csrf_token() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                ClientManager.showAlert('Cache cleared successfully!', 'success');
            } else {
                ClientManager.showAlert('Failed to clear cache.', 'error');
            }
        });
    }
}

function resetSystem() {
    if (confirm('Are you ABSOLUTELY sure you want to reset the entire system? This will delete ALL data and cannot be undone!')) {
        if (confirm('This is your final warning. All clients, domains, servers, and settings will be permanently deleted. Continue?')) {
            window.location.href = '<?= url('/settings/reset') ?>';
        }
    }
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
