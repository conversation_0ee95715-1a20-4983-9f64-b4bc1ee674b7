<?php
/**
 * Domain Model
 */

class Domain extends Model
{
    protected $table = 'domains';
    protected $fillable = [
        'client_id', 'mediator_id', 'domain_name', 'registrar', 'purchase_date',
        'expiry_date', 'current_cost', 'auto_renewal', 'nameservers', 'status', 'notes'
    ];
    
    /**
     * Get domain with client and mediator info
     */
    public function getDomainWithRelations($id)
    {
        $sql = "SELECT d.*, c.name as client_name, c.email as client_email, c.phone as client_phone,
                       m.name as mediator_name, m.email as mediator_email, m.phone as mediator_phone
                FROM domains d
                LEFT JOIN clients c ON d.client_id = c.id
                LEFT JOIN mediators m ON d.mediator_id = m.id
                WHERE d.id = ?";

        $result = $this->query($sql, [$id]);
        return $result[0] ?? null;
    }
    
    /**
     * Get domains with client info
     */
    public function getDomainsWithClients($conditions = [], $orderBy = 'expiry_date ASC')
    {
        $sql = "SELECT d.*, c.name as client_name, c.email as client_email
                FROM domains d
                LEFT JOIN clients c ON d.client_id = c.id";
        
        $params = [];
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "d.{$field} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY d.{$orderBy}";
        }
        
        return $this->query($sql, $params);
    }
    
    /**
     * Get expiring domains
     */
    public function getExpiringDomains($days = 30)
    {
        $date = date('Y-m-d', strtotime("+{$days} days"));
        
        $sql = "SELECT d.*, c.name as client_name, c.email as client_email,
                       DATEDIFF(d.expiry_date, CURDATE()) as days_until_expiry
                FROM domains d
                LEFT JOIN clients c ON d.client_id = c.id
                WHERE d.expiry_date <= ? AND d.status = 'active'
                ORDER BY d.expiry_date ASC";
        
        return $this->query($sql, [$date]);
    }
    
    /**
     * Get expired domains
     */
    public function getExpiredDomains()
    {
        $sql = "SELECT d.*, c.name as client_name, c.email as client_email,
                       DATEDIFF(CURDATE(), d.expiry_date) as days_expired
                FROM domains d
                LEFT JOIN clients c ON d.client_id = c.id
                WHERE d.expiry_date < CURDATE() AND d.status = 'active'
                ORDER BY d.expiry_date ASC";
        
        return $this->query($sql);
    }
    
    /**
     * Search domains
     */
    public function searchDomains($query)
    {
        $sql = "SELECT d.*, c.name as client_name
                FROM domains d
                LEFT JOIN clients c ON d.client_id = c.id
                WHERE d.domain_name LIKE ? OR d.registrar LIKE ? OR c.name LIKE ?
                ORDER BY d.domain_name ASC";
        
        $searchTerm = "%{$query}%";
        return $this->query($sql, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * Get domain statistics
     */
    public function getStats()
    {
        $stats = [
            'total_domains' => $this->count(),
            'active_domains' => $this->count(['status' => 'active']),
            'expired_domains' => $this->count(['status' => 'expired']),
            'suspended_domains' => $this->count(['status' => 'suspended'])
        ];
        
        // Get expiring domains count
        $expiringCount = $this->query(
            "SELECT COUNT(*) as count FROM domains 
             WHERE expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) 
             AND status = 'active'"
        );
        $stats['expiring_30_days'] = $expiringCount[0]['count'] ?? 0;
        
        // Get total current cost
        $totalCost = $this->query(
            "SELECT SUM(current_cost) as total FROM domains WHERE status = 'active'"
        );
        $stats['total_current_cost'] = $totalCost[0]['total'] ?? 0;
        
        return $stats;
    }
    
    /**
     * Get domains by registrar
     */
    public function getDomainsByRegistrar()
    {
        $sql = "SELECT registrar, COUNT(*) as count, SUM(current_cost) as total_cost
                FROM domains
                WHERE status = 'active' AND registrar IS NOT NULL
                GROUP BY registrar
                ORDER BY count DESC";
        
        return $this->query($sql);
    }
    
    /**
     * Update domain status based on expiry
     */
    public function updateExpiredDomains()
    {
        $sql = "UPDATE domains SET status = 'expired'
                WHERE expiry_date < CURDATE() AND status = 'active'";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute();
    }

    /**
     * Get domains for a specific client
     */
    public function getClientDomains($clientId)
    {
        return $this->where(['client_id' => $clientId, 'status' => 'active'], 'domain_name ASC');
    }
    
    /**
     * Get renewal calendar
     */
    public function getRenewalCalendar($year = null, $month = null)
    {
        $year = $year ?: date('Y');
        $month = $month ?: date('m');
        
        $sql = "SELECT d.*, c.name as client_name
                FROM domains d
                LEFT JOIN clients c ON d.client_id = c.id
                WHERE YEAR(d.expiry_date) = ? AND MONTH(d.expiry_date) = ?
                AND d.status = 'active'
                ORDER BY d.expiry_date ASC";
        
        return $this->query($sql, [$year, $month]);
    }
}
