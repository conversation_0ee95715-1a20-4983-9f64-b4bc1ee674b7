<?php
/**
 * Dashboard Controller
 */

class DashboardController extends Controller
{
    private $clientModel;
    private $domainModel;
    private $serverModel;
    private $mediatorModel;
    
    public function __construct()
    {
        parent::__construct();
        
        $this->clientModel = new Client();
        $this->domainModel = new Domain();
        $this->serverModel = new Server();
        $this->mediatorModel = new Mediator();
    }
    
    /**
     * Dashboard index
     */
    public function index()
    {
        // Get cached dashboard data
        $cacheKey = 'dashboard_data_' . Auth::id();
        $dashboardData = $this->cache->remember($cacheKey, function() {
            return $this->getDashboardData();
        }, 300); // Cache for 5 minutes
        
        echo $this->view('dashboard/index', [
            'title' => 'Dashboard',
            'data' => $dashboardData,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Get dashboard data
     */
    private function getDashboardData()
    {
        // Get statistics
        $stats = [
            'clients' => $this->clientModel->getStats(),
            'domains' => $this->domainModel->getStats(),
            'servers' => $this->serverModel->getStats(),
            'mediators' => $this->mediatorModel->getStats()
        ];
        
        // Get expiring items
        $expiringDomains = $this->domainModel->getExpiringDomains(30);
        $expiringServers = $this->serverModel->getExpiringServers(30);
        
        // Get expired items
        $expiredDomains = $this->domainModel->getExpiredDomains();
        $expiredServers = $this->serverModel->getExpiredServers();
        
        // Get recent activities (you might want to implement this)
        $recentActivities = $this->getRecentActivities();
        
        // Get revenue summary
        $revenueSummary = $this->getRevenueSummary();
        
        // Get upcoming renewals (next 7 days)
        $upcomingRenewals = $this->getUpcomingRenewals();
        
        // Get top clients by value
        $topClients = $this->getTopClients();
        
        // Get recent domains and servers
        $recentDomains = $this->getRecentDomains();
        $recentServers = $this->getRecentServers();

        return [
            'stats' => $stats,
            'expiring_domains' => $expiringDomains,
            'expiring_servers' => $expiringServers,
            'expired_domains' => $expiredDomains,
            'expired_servers' => $expiredServers,
            'recent_domains' => $recentDomains,
            'recent_servers' => $recentServers,
            'recent_activities' => $recentActivities,
            'revenue_summary' => $revenueSummary,
            'upcoming_renewals' => $upcomingRenewals,
            'top_clients' => $topClients
        ];
    }
    
    /**
     * Get recent domains
     */
    private function getRecentDomains($limit = 10)
    {
        $db = Database::getInstance()->getConnection();

        $sql = "SELECT d.*, c.name as client_name
                FROM domains d
                LEFT JOIN clients c ON d.client_id = c.id
                WHERE d.status = 'active'
                ORDER BY d.created_at DESC
                LIMIT ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$limit]);

        return $stmt->fetchAll();
    }

    /**
     * Get recent servers
     */
    private function getRecentServers($limit = 10)
    {
        $db = Database::getInstance()->getConnection();

        $sql = "SELECT s.*, c.name as client_name
                FROM servers s
                LEFT JOIN clients c ON s.client_id = c.id
                WHERE s.status = 'active'
                ORDER BY s.created_at DESC
                LIMIT ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$limit]);

        return $stmt->fetchAll();
    }
    
    /**
     * Get revenue summary
     */
    private function getRevenueSummary()
    {
        $db = Database::getInstance()->getConnection();
        
        // Get total domain revenue
        $domainRevenue = $db->query("
            SELECT SUM(current_cost) as total
            FROM domains
            WHERE status = 'active'
        ")->fetchColumn();

        // Get total server revenue
        $serverRevenue = $db->query("
            SELECT SUM(current_cost) as total
            FROM servers
            WHERE status = 'active'
        ")->fetchColumn();
        
        // Get monthly breakdown
        $monthlyBreakdown = $db->query("
            SELECT
                MONTH(purchase_date) as month,
                YEAR(purchase_date) as year,
                SUM(current_cost) as total
            FROM (
                SELECT purchase_date, current_cost FROM domains WHERE status = 'active'
                UNION ALL
                SELECT purchase_date, current_cost FROM servers WHERE status = 'active'
            ) as combined
            WHERE purchase_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
            GROUP BY YEAR(purchase_date), MONTH(purchase_date)
            ORDER BY year DESC, month DESC
        ")->fetchAll();
        
        return [
            'domain_revenue' => $domainRevenue ?: 0,
            'server_revenue' => $serverRevenue ?: 0,
            'total_revenue' => ($domainRevenue ?: 0) + ($serverRevenue ?: 0),
            'monthly_breakdown' => $monthlyBreakdown
        ];
    }
    
    /**
     * Get upcoming renewals
     */
    private function getUpcomingRenewals($days = 7)
    {
        $date = date('Y-m-d', strtotime("+{$days} days"));
        
        $domains = $this->domainModel->query("
            SELECT 'domain' as type, d.domain_name as name, d.expiry_date,
                   d.current_cost, c.name as client_name
            FROM domains d
            LEFT JOIN clients c ON d.client_id = c.id
            WHERE d.expiry_date <= ? AND d.status = 'active'
            ORDER BY d.expiry_date ASC
        ", [$date]);

        $servers = $this->serverModel->query("
            SELECT 'server' as type, s.server_name as name, s.expiry_date,
                   s.current_cost, c.name as client_name
            FROM servers s
            LEFT JOIN clients c ON s.client_id = c.id
            WHERE s.expiry_date <= ? AND s.status = 'active'
            ORDER BY s.expiry_date ASC
        ", [$date]);
        
        // Combine and sort
        $renewals = array_merge($domains, $servers);
        usort($renewals, function($a, $b) {
            return strtotime($a['expiry_date']) - strtotime($b['expiry_date']);
        });
        
        return $renewals;
    }
    
    /**
     * Get top clients by value
     */
    private function getTopClients($limit = 5)
    {
        $db = Database::getInstance()->getConnection();
        
        $sql = "SELECT c.*,
                       COUNT(d.id) as domain_count,
                       COUNT(s.id) as server_count,
                       SUM(COALESCE(d.current_cost, 0)) + SUM(COALESCE(s.current_cost, 0)) as total_value
                FROM clients c
                LEFT JOIN domains d ON c.id = d.client_id AND d.status = 'active'
                LEFT JOIN servers s ON c.id = s.client_id AND s.status = 'active'
                WHERE c.is_active = 1
                GROUP BY c.id
                HAVING total_value > 0
                ORDER BY total_value DESC
                LIMIT ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get recent activities
     */
    private function getRecentActivities($limit = 10)
    {
        $db = Database::getInstance()->getConnection();

        $sql = "SELECT al.*, u.first_name, u.last_name, u.username
                FROM audit_logs al
                LEFT JOIN users u ON al.user_id = u.id
                ORDER BY al.created_at DESC
                LIMIT ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$limit]);

        $activities = $stmt->fetchAll();

        // Format activities for display
        foreach ($activities as &$activity) {
            $activity['user_display'] = $activity['first_name'] . ' ' . $activity['last_name'];
            $activity['action_display'] = $this->formatActivityAction($activity['action'], $activity['table_name']);
            $activity['time_ago'] = $this->timeAgo($activity['created_at']);
        }

        return $activities;
    }

    /**
     * Format activity action for display
     */
    private function formatActivityAction($action, $tableName)
    {
        $actionMap = [
            'login' => 'Logged in',
            'logout' => 'Logged out',
            'create' => 'Created',
            'update' => 'Updated',
            'delete' => 'Deleted',
            'view' => 'Viewed'
        ];

        $tableMap = [
            'users' => 'user',
            'clients' => 'client',
            'domains' => 'domain',
            'servers' => 'server',
            'mediators' => 'mediator'
        ];

        $actionText = $actionMap[$action] ?? ucfirst($action);
        $tableText = $tableMap[$tableName] ?? $tableName;

        if ($action === 'login' || $action === 'logout') {
            return $actionText;
        }

        return $actionText . ' ' . $tableText;
    }

    /**
     * Get time ago string
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) {
            return 'Just now';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } else {
            return date('M j, Y', strtotime($datetime));
        }
    }

    /**
     * Get chart data for domains/servers over time
     */
    public function getChartData()
    {
        $db = Database::getInstance()->getConnection();

        // Get monthly data for the last 12 months
        $sql = "SELECT
                    DATE_FORMAT(purchase_date, '%Y-%m') as month,
                    COUNT(CASE WHEN 'domains' THEN 1 END) as domains,
                    COUNT(CASE WHEN 'servers' THEN 1 END) as servers
                FROM (
                    SELECT purchase_date, 'domains' as type FROM domains
                    UNION ALL
                    SELECT purchase_date, 'servers' as type FROM servers
                ) as combined
                WHERE purchase_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(purchase_date, '%Y-%m')
                ORDER BY month ASC";

        $result = $db->query($sql)->fetchAll();

        $this->json([
            'success' => true,
            'data' => $result
        ]);
    }
}
