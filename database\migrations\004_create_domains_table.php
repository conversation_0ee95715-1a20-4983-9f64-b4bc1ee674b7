<?php
$sql = "CREATE TABLE IF NOT EXISTS domains (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    mediator_id INT,
    domain_name VARCHAR(255) NOT NULL,
    registrar VARCHAR(100),
    purchase_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    current_cost DECIMAL(10,2),
    auto_renewal BOOLEAN DEFAULT FALSE,
    nameservers TEXT,
    status ENUM('active', 'expired', 'suspended', 'transferred') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (mediator_id) REFERENCES mediators(id) ON DELETE SET NULL
)";

$this->connection->exec($sql);

// Rename renewal_cost to current_cost if the table already exists
try {
    $this->connection->exec("ALTER TABLE domains CHANGE renewal_cost current_cost DECIMAL(10,2)");
} catch (PDOException $e) {
    // Column might not exist or already renamed, ignore error
}
