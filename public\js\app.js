/**
 * Client Manager System - Main JavaScript File
 */

// Global app object
window.ClientManager = {
    config: window.appConfig || {},
    
    // Initialize the application
    init: function() {
        this.setupCSRF();
        this.setupAjax();
        this.setupFormValidation();
        this.setupDataTables();
        this.setupTooltips();
        this.setupConfirmDialogs();
        this.setupAutoSave();
        this.setupThemeToggle();
        this.setupSearchFilters();
        this.setupSidebar();
    },
    
    // Setup CSRF token for all AJAX requests
    setupCSRF: function() {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': this.config.csrfToken
            }
        });
    },
    
    // Setup global AJAX handlers
    setupAjax: function() {
        // Show loading indicator for AJAX requests
        $(document).ajaxStart(function() {
            $('#loading-indicator').show();
        }).ajaxStop(function() {
            $('#loading-indicator').hide();
        });
        
        // Handle AJAX errors
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status === 419) {
                // CSRF token mismatch
                ClientManager.showAlert('Session expired. Please refresh the page.', 'error');
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else if (xhr.status === 403) {
                ClientManager.showAlert('Access denied.', 'error');
            } else if (xhr.status === 500) {
                ClientManager.showAlert('Server error. Please try again.', 'error');
            }
        });
    },
    
    // Setup form validation
    setupFormValidation: function() {
        // Real-time validation
        $('form').on('input', 'input, textarea, select', function() {
            const field = $(this);
            const value = field.val();
            const rules = field.data('validation');
            
            if (rules) {
                ClientManager.validateField(field, value, rules);
            }
        });
        
        // Form submission validation
        $('form').on('submit', function(e) {
            const form = $(this);
            let isValid = true;
            
            form.find('[data-validation]').each(function() {
                const field = $(this);
                const value = field.val();
                const rules = field.data('validation');
                
                if (!ClientManager.validateField(field, value, rules)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                ClientManager.showAlert('Please correct the errors below.', 'error');
            }
        });
    },
    
    // Validate individual field
    validateField: function(field, value, rules) {
        const ruleList = rules.split('|');
        let isValid = true;
        let errorMessage = '';
        
        for (let rule of ruleList) {
            const [ruleName, ruleValue] = rule.split(':');
            
            switch (ruleName) {
                case 'required':
                    if (!value || value.trim() === '') {
                        isValid = false;
                        errorMessage = 'This field is required.';
                    }
                    break;
                    
                case 'email':
                    if (value && !this.isValidEmail(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address.';
                    }
                    break;
                    
                case 'min':
                    if (value && value.length < parseInt(ruleValue)) {
                        isValid = false;
                        errorMessage = `Minimum ${ruleValue} characters required.`;
                    }
                    break;
                    
                case 'max':
                    if (value && value.length > parseInt(ruleValue)) {
                        isValid = false;
                        errorMessage = `Maximum ${ruleValue} characters allowed.`;
                    }
                    break;
                    
                case 'numeric':
                    if (value && !this.isNumeric(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid number.';
                    }
                    break;
                    
                case 'date':
                    if (value && !this.isValidDate(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid date.';
                    }
                    break;
            }
            
            if (!isValid) break;
        }
        
        // Update field appearance
        if (isValid) {
            field.removeClass('is-invalid').addClass('is-valid');
            field.siblings('.invalid-feedback').hide();
        } else {
            field.removeClass('is-valid').addClass('is-invalid');
            let feedback = field.siblings('.invalid-feedback');
            if (feedback.length === 0) {
                feedback = $('<div class="invalid-feedback"></div>');
                field.after(feedback);
            }
            feedback.text(errorMessage).show();
        }
        
        return isValid;
    },
    
    // Setup DataTables
    setupDataTables: function() {
        if ($.fn.DataTable) {
            $('.data-table').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        }
    },
    
    // Setup tooltips
    setupTooltips: function() {
        $('[data-bs-toggle="tooltip"]').tooltip();
    },
    
    // Setup confirmation dialogs
    setupConfirmDialogs: function() {
        $('[data-confirm]').on('click', function(e) {
            e.preventDefault();
            const message = $(this).data('confirm');
            const href = $(this).attr('href');
            
            if (confirm(message)) {
                if (href) {
                    window.location.href = href;
                } else {
                    $(this).closest('form').submit();
                }
            }
        });
    },
    
    // Setup auto-save functionality
    setupAutoSave: function() {
        $('[data-autosave]').on('input', function() {
            const field = $(this);
            const form = field.closest('form');
            const url = form.data('autosave-url');
            
            if (url) {
                clearTimeout(field.data('autosave-timer'));
                field.data('autosave-timer', setTimeout(function() {
                    ClientManager.autoSave(form, url);
                }, 2000));
            }
        });
    },
    
    // Auto-save form data
    autoSave: function(form, url) {
        const data = form.serialize();
        
        $.post(url, data)
            .done(function(response) {
                if (response.success) {
                    ClientManager.showAlert('Changes saved automatically.', 'success', 2000);
                }
            })
            .fail(function() {
                ClientManager.showAlert('Auto-save failed.', 'error', 2000);
            });
    },
    
    // Setup theme toggle
    setupThemeToggle: function() {
        $('#theme-toggle').on('click', function() {
            $('body').toggleClass('dark-mode');
            const isDark = $('body').hasClass('dark-mode');
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
        });
        
        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            $('body').addClass('dark-mode');
        }
    },
    
    // Setup search filters
    setupSearchFilters: function() {
        $('.search-filter').on('input', function() {
            const query = $(this).val().toLowerCase();
            const target = $(this).data('target');

            $(target).each(function() {
                const text = $(this).text().toLowerCase();
                $(this).toggle(text.includes(query));
            });
        });
    },

    // Setup sidebar functionality
    setupSidebar: function() {
        const sidebar = $('#sidebar');
        const sidebarToggle = $('#sidebar-toggle');
        const sidebarOverlay = $('#sidebar-overlay');

        // Toggle sidebar on mobile
        sidebarToggle.on('click', function() {
            sidebar.toggleClass('show');
            sidebarOverlay.toggleClass('show');
        });

        // Close sidebar when clicking overlay
        sidebarOverlay.on('click', function() {
            sidebar.removeClass('show');
            sidebarOverlay.removeClass('show');
        });

        // Close sidebar on window resize if desktop
        $(window).on('resize', function() {
            if ($(window).width() >= 992) {
                sidebar.removeClass('show');
                sidebarOverlay.removeClass('show');
            }
        });

        // Handle sidebar navigation active states
        const currentPath = window.location.pathname;
        $('.nav-sidebar .nav-link').each(function() {
            const link = $(this);
            const href = link.attr('href');

            if (href && currentPath.includes(href.split('/').pop())) {
                link.addClass('active');
            }
        });
    },
    
    // Utility functions
    isValidEmail: function(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },
    
    isNumeric: function(value) {
        return !isNaN(value) && !isNaN(parseFloat(value));
    },
    
    isValidDate: function(date) {
        return !isNaN(Date.parse(date));
    },
    
    // Show alert message
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertClass = type === 'error' ? 'danger' : type;
        const alert = $(`
            <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('#alert-container').append(alert);
        
        if (duration > 0) {
            setTimeout(function() {
                alert.alert('close');
            }, duration);
        }
    },
    
    // Format currency
    formatCurrency: function(amount, currency = '₹') {
        return currency + parseFloat(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    },
    
    // Format date
    formatDate: function(date, format = 'Y-m-d') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        switch (format) {
            case 'Y-m-d':
                return `${year}-${month}-${day}`;
            case 'M j, Y':
                return d.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric' 
                });
            default:
                return d.toLocaleDateString();
        }
    },
    
    // Calculate days until date
    daysUntil: function(date) {
        const today = new Date();
        const target = new Date(date);
        const diffTime = target - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
    },
    
    // Export table to CSV
    exportTableToCSV: function(tableId, filename = 'export.csv') {
        const table = document.getElementById(tableId);
        const rows = table.querySelectorAll('tr');
        const csv = [];
        
        for (let i = 0; i < rows.length; i++) {
            const row = [];
            const cols = rows[i].querySelectorAll('td, th');
            
            for (let j = 0; j < cols.length; j++) {
                row.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
            }
            
            csv.push(row.join(','));
        }
        
        const csvContent = csv.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }
};

// Initialize when document is ready
$(document).ready(function() {
    ClientManager.init();
    
    // Add loading indicator if not exists
    if ($('#loading-indicator').length === 0) {
        $('body').append(`
            <div id="loading-indicator" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `);
    }
    
    // Add alert container if not exists
    if ($('#alert-container').length === 0) {
        $('main').prepend('<div id="alert-container"></div>');
    }
});
