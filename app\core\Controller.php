<?php
/**
 * Base Controller Class
 * Provides common functionality for all controllers
 */

class Controller
{
    protected $view;
    protected $cache;
    protected $config;
    
    public function __construct()
    {
        $this->view = new View();
        $this->cache = new Cache();
        $this->config = include CONFIG_PATH . '/config.php';
        
        // Set global view variables
        $this->view->set('config', $this->config);
        $this->view->set('user', Auth::user());
        $this->view->set('isLoggedIn', Auth::check());
    }
    
    /**
     * Render a view
     */
    protected function view($template, $data = [])
    {
        return $this->view->render($template, $data);
    }
    
    /**
     * Return JSON response
     */
    protected function json($data, $status = 200)
    {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Redirect to URL
     */
    protected function redirect($url, $message = null, $type = 'info')
    {
        if ($message) {
            $this->setFlashMessage($message, $type);
        }
        
        $baseUrl = $this->config['app']['url'];
        
        if (strpos($url, 'http') !== 0) {
            $url = rtrim($baseUrl, '/') . '/' . ltrim($url, '/');
        }
        
        header("Location: $url");
        exit;
    }
    
    /**
     * Set flash message
     */
    protected function setFlashMessage($message, $type = 'info')
    {
        if (!isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = [];
        }
        
        $_SESSION['flash_messages'][] = [
            'message' => $message,
            'type' => $type
        ];
    }
    
    /**
     * Get flash messages
     */
    protected function getFlashMessages()
    {
        $messages = $_SESSION['flash_messages'] ?? [];
        unset($_SESSION['flash_messages']);
        return $messages;
    }
    
    /**
     * Validate request data
     */
    protected function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = explode('|', $rule);
            
            foreach ($ruleList as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule, $data);
                if ($error) {
                    $errors[$field] = $error;
                    break; // Stop at first error for this field
                }
            }
        }
        
        if (!empty($errors)) {
            $this->setFlashMessage('Please correct the errors below.', 'error');
            $_SESSION['validation_errors'] = $errors;
            $_SESSION['old_input'] = $data;
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate individual field
     */
    private function validateField($field, $value, $rule, $allData)
    {
        if (strpos($rule, ':') !== false) {
            list($ruleName, $ruleValue) = explode(':', $rule, 2);
        } else {
            $ruleName = $rule;
            $ruleValue = null;
        }
        
        switch ($ruleName) {
            case 'required':
                if (empty($value)) {
                    return ucfirst(str_replace('_', ' ', $field)) . ' is required.';
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return 'Please enter a valid email address.';
                }
                break;
                
            case 'min':
                if (!empty($value) && strlen($value) < $ruleValue) {
                    return ucfirst(str_replace('_', ' ', $field)) . " must be at least {$ruleValue} characters.";
                }
                break;
                
            case 'max':
                if (!empty($value) && strlen($value) > $ruleValue) {
                    return ucfirst(str_replace('_', ' ', $field)) . " must not exceed {$ruleValue} characters.";
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    return ucfirst(str_replace('_', ' ', $field)) . ' must be a number.';
                }
                break;
                
            case 'date':
                if (!empty($value) && !strtotime($value)) {
                    return ucfirst(str_replace('_', ' ', $field)) . ' must be a valid date.';
                }
                break;
                
            case 'unique':
                if (!empty($value)) {
                    list($table, $column) = explode(',', $ruleValue);
                    $db = Database::getInstance()->getConnection();
                    $stmt = $db->prepare("SELECT COUNT(*) FROM {$table} WHERE {$column} = ?");
                    $stmt->execute([$value]);
                    if ($stmt->fetchColumn() > 0) {
                        return ucfirst(str_replace('_', ' ', $field)) . ' already exists.';
                    }
                }
                break;
                
            case 'confirmed':
                $confirmField = $field . '_confirmation';
                if ($value !== ($allData[$confirmField] ?? null)) {
                    return ucfirst(str_replace('_', ' ', $field)) . ' confirmation does not match.';
                }
                break;
        }
        
        return null;
    }
    
    /**
     * Get validation errors
     */
    protected function getValidationErrors()
    {
        $errors = $_SESSION['validation_errors'] ?? [];
        unset($_SESSION['validation_errors']);
        return $errors;
    }
    
    /**
     * Get old input
     */
    protected function getOldInput($key = null, $default = null)
    {
        $oldInput = $_SESSION['old_input'] ?? [];
        
        if ($key === null) {
            unset($_SESSION['old_input']);
            return $oldInput;
        }
        
        return $oldInput[$key] ?? $default;
    }
    
    /**
     * Check CSRF token
     */
    protected function checkCsrfToken()
    {
        $token = $_POST['_token'] ?? $_GET['_token'] ?? null;
        
        if (!$token || !hash_equals($_SESSION['csrf_token'] ?? '', $token)) {
            http_response_code(419);
            die('CSRF token mismatch');
        }
    }
    
    /**
     * Generate CSRF token
     */
    protected function generateCsrfToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Paginate results
     */
    protected function paginate($query, $page = 1, $perPage = null)
    {
        $perPage = $perPage ?: $this->config['pagination']['per_page'];
        $page = max(1, (int)$page);
        $offset = ($page - 1) * $perPage;
        
        // Get total count
        $countQuery = preg_replace('/SELECT .+ FROM/i', 'SELECT COUNT(*) FROM', $query);
        $db = Database::getInstance()->getConnection();
        $totalItems = $db->query($countQuery)->fetchColumn();
        
        // Add limit and offset
        $query .= " LIMIT {$perPage} OFFSET {$offset}";
        
        return [
            'query' => $query,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_items' => $totalItems,
                'total_pages' => ceil($totalItems / $perPage),
                'has_previous' => $page > 1,
                'has_next' => $page < ceil($totalItems / $perPage)
            ]
        ];
    }
}
