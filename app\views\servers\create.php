<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-server me-2 text-primary"></i>
                Add New Server
            </h1>
            <a href="<?= url('/servers') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Servers
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Server Information
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/servers') ?>" id="serverForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_id" class="form-label">
                                    Client <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('client_id') ? 'is-invalid' : '' ?>" 
                                        id="client_id" 
                                        name="client_id" 
                                        required>
                                    <option value="">Select a client</option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?= $client['id'] ?>" <?= old('client_id') == $client['id'] ? 'selected' : '' ?>>
                                            <?= e($client['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('client_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('client_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mediator_id" class="form-label">Mediator</label>
                                <select class="form-select <?= has_error('mediator_id') ? 'is-invalid' : '' ?>" 
                                        id="mediator_id" 
                                        name="mediator_id">
                                    <option value="">No mediator</option>
                                    <?php foreach ($mediators as $mediator): ?>
                                        <option value="<?= $mediator['id'] ?>" <?= old('mediator_id') == $mediator['id'] ? 'selected' : '' ?>>
                                            <?= e($mediator['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('mediator_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('mediator_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="domain_id" class="form-label">
                                    Domain <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('domain_id') ? 'is-invalid' : '' ?>"
                                        id="domain_id"
                                        name="domain_id"
                                        required>
                                    <option value="">Select a domain</option>
                                </select>
                                <?php if (has_error('domain_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('domain_id')) ?>
                                    </div>
                                <?php endif; ?>
                                <div class="form-text">Server will be named after the selected domain</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="provider" class="form-label">Provider</label>
                                <input type="text" 
                                       class="form-control <?= has_error('provider') ? 'is-invalid' : '' ?>" 
                                       id="provider" 
                                       name="provider" 
                                       value="<?= e(old('provider')) ?>"
                                       placeholder="AWS, DigitalOcean, etc.">
                                <?php if (has_error('provider')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('provider')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="server_type" class="form-label">
                                    Server Type <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('server_type') ? 'is-invalid' : '' ?>" 
                                        id="server_type" 
                                        name="server_type" 
                                        required>
                                    <option value="">Select server type</option>
                                    <?php foreach ($server_types as $type): ?>
                                        <option value="<?= $type ?>" <?= old('server_type') == $type ? 'selected' : '' ?>>
                                            <?= ucfirst($type) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('server_type')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('server_type')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ip_address" class="form-label">IP Address</label>
                                <input type="text" 
                                       class="form-control <?= has_error('ip_address') ? 'is-invalid' : '' ?>" 
                                       id="ip_address" 
                                       name="ip_address" 
                                       value="<?= e(old('ip_address')) ?>"
                                       placeholder="***********">
                                <?php if (has_error('ip_address')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('ip_address')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="purchase_date" class="form-label">
                                    Purchase Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control <?= has_error('purchase_date') ? 'is-invalid' : '' ?>" 
                                       id="purchase_date" 
                                       name="purchase_date" 
                                       value="<?= e(old('purchase_date', date('Y-m-d'))) ?>"
                                       required>
                                <?php if (has_error('purchase_date')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('purchase_date')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="years" class="form-label">
                                    Service Period <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('years') ? 'is-invalid' : '' ?>"
                                        id="years"
                                        name="years"
                                        required>
                                    <option value="">Select years</option>
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <option value="<?= $i ?>" <?= old('years') == $i ? 'selected' : '' ?>>
                                            <?= $i ?> Year<?= $i > 1 ? 's' : '' ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                                <?php if (has_error('years')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('years')) ?>
                                    </div>
                                <?php endif; ?>
                                <div class="form-text">Server will be purchased for the selected period</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="current_cost" class="form-label">Current Cost</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number"
                                           class="form-control <?= has_error('current_cost') ? 'is-invalid' : '' ?>"
                                           id="current_cost"
                                           name="current_cost"
                                           value="<?= e(old('current_cost')) ?>"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                    <?php if (has_error('current_cost')): ?>
                                        <div class="invalid-feedback">
                                            <?= e(error('current_cost')) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="form-text">Amount you are charging the client now</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="auto_renewal" 
                                           name="auto_renewal" 
                                           value="1"
                                           <?= old('auto_renewal') ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="auto_renewal">
                                        Enable Auto Renewal
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="specifications" class="form-label">Server Specifications</label>
                        <textarea class="form-control <?= has_error('specifications') ? 'is-invalid' : '' ?>" 
                                  id="specifications" 
                                  name="specifications" 
                                  rows="3"
                                  placeholder="CPU: 2 cores, RAM: 4GB, Storage: 80GB SSD"><?= e(old('specifications')) ?></textarea>
                        <?php if (has_error('specifications')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('specifications')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control <?= has_error('notes') ? 'is-invalid' : '' ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4"
                                  placeholder="Additional notes about the server"><?= e(old('notes')) ?></textarea>
                        <?php if (has_error('notes')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('notes')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= url('/servers') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Add Server
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-1"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> Server Types</h6>
                    <ul class="mb-0 small">
                        <li><strong>Shared:</strong> Basic hosting plans</li>
                        <li><strong>VPS:</strong> Virtual private servers</li>
                        <li><strong>Dedicated:</strong> Physical servers</li>
                        <li><strong>Cloud:</strong> Cloud-based instances</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Renewal Alerts</h6>
                    <p class="mb-0 small">
                        The system will automatically send alerts before 
                        the server expires to prevent service interruption.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Embed all domains data directly in the page
const allDomainsData = <?= json_encode($all_domains ?? []) ?>;

document.addEventListener('DOMContentLoaded', function() {
    // Load domains when client is selected
    const clientSelect = document.getElementById('client_id');
    const domainSelect = document.getElementById('domain_id');

    if (clientSelect && domainSelect) {
        clientSelect.addEventListener('change', function() {
            const clientId = this.value;

            domainSelect.innerHTML = '<option value="">Select a domain</option>';

            if (clientId) {
                // Filter domains for the selected client from embedded data
                const clientDomains = allDomainsData.filter(domain =>
                    domain.client_id == clientId && domain.status === 'active'
                );

                if (clientDomains.length > 0) {
                    clientDomains.forEach(function(domain) {
                        const option = document.createElement('option');
                        option.value = domain.id;
                        option.textContent = domain.domain_name;
                        domainSelect.appendChild(option);
                    });
                } else {
                    domainSelect.innerHTML = '<option value="">No domains found for this client</option>';
                }
            }
        });
    }


    // Show calculated expiry date when years or purchase date changes
    function updateExpiryDisplay() {
        const purchaseDateField = document.getElementById('purchase_date');
        const yearsField = document.getElementById('years');

        if (!purchaseDateField || !yearsField) return;

        const purchaseDate = purchaseDateField.value;
        const years = yearsField.value;

        if (purchaseDate && years) {
            const purchase = new Date(purchaseDate);
            const expiry = new Date(purchase);
            expiry.setFullYear(expiry.getFullYear() + parseInt(years));

            // Show expiry date info
            const expiryText = expiry.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            let expiryInfo = document.getElementById('expiry-info');
            if (!expiryInfo) {
                expiryInfo = document.createElement('div');
                expiryInfo.id = 'expiry-info';
                expiryInfo.className = 'form-text text-success mt-1';
                yearsField.parentNode.appendChild(expiryInfo);
            }
            expiryInfo.innerHTML = '<i class="fas fa-calendar-check me-1"></i>Server will expire on: ' + expiryText;
        } else {
            const expiryInfo = document.getElementById('expiry-info');
            if (expiryInfo) {
                expiryInfo.remove();
            }
        }
    }

    const purchaseDateField = document.getElementById('purchase_date');
    const yearsField = document.getElementById('years');
    if (purchaseDateField) purchaseDateField.addEventListener('change', updateExpiryDisplay);
    if (yearsField) yearsField.addEventListener('change', updateExpiryDisplay);

    // IP address validation
    const ipAddressField = document.getElementById('ip_address');
    if (ipAddressField) {
        ipAddressField.addEventListener('blur', function() {
            const ip = this.value.trim();
            if (ip) {
                const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                if (!ipRegex.test(ip)) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            }
        });
    }

    // Form validation
    const serverForm = document.getElementById('serverForm');
    if (serverForm) {
        serverForm.addEventListener('submit', function(e) {
            let isValid = true;

            // Check required fields
            const requiredFields = ['client_id', 'domain_id', 'server_type', 'purchase_date', 'years'];
            requiredFields.forEach(function(field) {
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    const value = fieldElement.value.trim();
                    if (!value) {
                        fieldElement.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        fieldElement.classList.remove('is-invalid');
                    }
                }
            });

            if (!isValid) {
                e.preventDefault();
                if (typeof ClientManager !== 'undefined') {
                    ClientManager.showAlert('Please correct the errors below.', 'error');
                } else {
                    alert('Please correct the errors below.');
                }
            }
        });
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
